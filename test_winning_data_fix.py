#!/usr/bin/env python3
"""
测试获胜实验数据修复
验证第2周没有获胜实验数据的问题是否已修复
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'test_winning_data_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def test_winning_data_registration():
    """测试获胜数据注册和检索"""
    logger = setup_logging()
    logger.info("开始测试获胜数据注册和检索")
    
    try:
        # 创建配置
        config = {
            "enhanced_shapley_oprp": {
                "enabled": True,
                "cycle_length_days": 7,
                "baseline_operation_days": 7,
                "ab_testing_days": 7
            }
        }
        
        # 创建管理器
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        logger.info("✅ 管理器创建成功")
        
        # 模拟A/B测试数据
        ab_test_data = {
            "week_number": 2,
            "test_agents": ["BeOA", "TAA"],
            "original_performance": {"BeOA": 0.8, "TAA": 0.7},
            "optimized_performance": {"BeOA": 0.9, "TAA": 0.8},
            "data_collected": True
        }
        
        # 模拟性能比较结果
        performance_comparison = {
            "ab_test_week_2": {
                "config": {"optimized_agents": ["BeOA", "TAA"]},
                "statistical_comparison": {
                    "recommendation": "optimized",
                    "improvement": 0.1,
                    "p_value": 0.01,
                    "statistical_significance": True,
                    "comparison_complete": True,
                    "winning_track": "optimized"
                }
            }
        }
        
        # 模拟提示词选择结果
        prompt_selection_result = {
            "ab_test_week_2": {
                "config": {"optimized_agents": ["BeOA", "TAA"]},
                "agent_selections": {
                    "BeOA": {
                        "selected_prompt": "optimized",
                        "reason": "优化提示词表现更好 (改进: 0.1000, p值: 0.0100)",
                        "statistical_data": {
                            "recommendation": "optimized",
                            "improvement": 0.1,
                            "p_value": 0.01
                        }
                    },
                    "TAA": {
                        "selected_prompt": "optimized",
                        "reason": "优化提示词表现更好 (改进: 0.1000, p值: 0.0100)",
                        "statistical_data": {
                            "recommendation": "optimized",
                            "improvement": 0.1,
                            "p_value": 0.01
                        }
                    }
                },
                "overall_recommendation": "optimized"
            }
        }
        
        logger.info("测试数据准备完成")
        
        # 测试注册获胜结果到迭代Shapley计算器
        logger.info("测试注册获胜结果...")
        manager._register_winning_results_to_iterative_calculator(
            ab_test_data, performance_comparison, prompt_selection_result
        )
        
        # 测试生成周期总结
        logger.info("测试生成周期总结...")
        weekly_summary = manager._finalize_weekly_cycle(
            ab_test_data, performance_comparison, prompt_selection_result
        )
        
        # 验证周期总结是否包含获胜实验数据
        if "winning_experiment_data" in weekly_summary:
            logger.info("✅ 周期总结包含获胜实验数据")
            winning_data = weekly_summary["winning_experiment_data"]
            logger.info(f"获胜实验数据: {winning_data}")
            
            # 验证数据完整性
            required_fields = ["ab_test_data", "performance_comparison", "prompt_selection_result", "target_agents", "week_number"]
            missing_fields = [field for field in required_fields if field not in winning_data]
            
            if not missing_fields:
                logger.info("✅ 获胜实验数据包含所有必需字段")
                return True
            else:
                logger.error(f"❌ 获胜实验数据缺少字段: {missing_fields}")
                return False
        else:
            logger.error("❌ 周期总结不包含获胜实验数据")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_iterative_shapley_data_availability():
    """测试迭代Shapley计算器中的数据可用性"""
    logger = logging.getLogger(__name__)
    logger.info("测试迭代Shapley计算器中的数据可用性...")
    
    try:
        config = {"enhanced_shapley_oprp": {}}
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        
        # 检查迭代Shapley计算器是否有数据
        calculator = manager.iterative_shapley_calculator
        
        # 检查实验数据记录
        num_records = len(calculator.experiment_data_records)
        logger.info(f"迭代Shapley计算器中有 {num_records} 个实验数据记录")
        
        # 检查获胜数据缓存
        cache_size = sum(len(records) for records in calculator.winning_data_cache.values())
        logger.info(f"获胜数据缓存中有 {cache_size} 个记录")
        
        # 尝试计算Shapley值
        target_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        shapley_result = calculator.calculate_iterative_shapley_values(
            target_agents=target_agents,
            use_cached_data=True
        )
        
        if shapley_result.get("success"):
            logger.info("✅ 迭代Shapley计算成功")
            return True
        else:
            logger.warning(f"⚠️ 迭代Shapley计算失败: {shapley_result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 60)
    logger.info("开始获胜实验数据修复验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1：获胜数据注册和检索
    logger.info("\n" + "=" * 40)
    logger.info("测试1：获胜数据注册和检索")
    logger.info("=" * 40)
    if test_winning_data_registration():
        success_count += 1
    
    # 测试2：迭代Shapley数据可用性
    logger.info("\n" + "=" * 40)
    logger.info("测试2：迭代Shapley数据可用性")
    logger.info("=" * 40)
    if test_iterative_shapley_data_availability():
        success_count += 1
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！获胜实验数据问题修复成功！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
