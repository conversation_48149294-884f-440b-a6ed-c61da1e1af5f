#!/usr/bin/env python3
"""
交易存储系统测试脚本

测试新实现的周期性交易存储管理器和OPRO集成功能

创建时间: 2025-07-06
作者: OPRO系统
"""

import os
import sys
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.weekly_trading_storage_manager import (
    WeeklyTradingStorageManager,
    AgentDecisionRecord,
    WeeklyPerformanceMetrics,
    ABTestTrackData
)
from data.opro_trading_data_integration import OPROTradingDataIntegration

def setup_test_logger() -> logging.Logger:
    """设置测试日志记录器"""
    logger = logging.getLogger("test_trading_storage")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def test_weekly_trading_storage_manager():
    """测试周期性交易存储管理器"""
    logger = setup_test_logger()
    logger.info("=" * 60)
    logger.info("🧪 开始测试周期性交易存储管理器")
    logger.info("=" * 60)
    
    try:
        # 初始化存储管理器
        storage_manager = WeeklyTradingStorageManager(
            base_data_dir="data/trading_test",
            config={"test_mode": True},
            logger=logger
        )
        
        # 测试1：开始周期记录
        logger.info("测试1: 开始周期记录...")
        week_id = storage_manager.start_weekly_cycle(
            week_number=1,
            week_type="baseline_operation",
            start_date="2025-01-01",
            end_date="2025-01-07",
            metadata={"test": "baseline_week"}
        )
        logger.info(f"✅ 周期ID: {week_id}")
        
        # 测试2：记录智能体决策
        logger.info("测试2: 记录智能体决策...")
        test_agents = ["TRA", "NSA", "FSA"]
        
        for i, agent_id in enumerate(test_agents):
            decision_record = AgentDecisionRecord(
                agent_id=agent_id,
                decision_type=["buy", "sell", "hold"][i % 3],
                decision_reason=f"测试决策原因 - {agent_id}",
                analysis_process=f"测试分析过程 - {agent_id}",
                confidence_score=0.8 + i * 0.05,
                timestamp=datetime.now().isoformat(),
                market_conditions={"test_condition": f"condition_{i}"},
                input_data={"test_input": f"input_{i}"},
                metadata={"test_metadata": f"metadata_{i}"}
            )
            
            success = storage_manager.record_agent_decision(decision_record, "baseline")
            logger.info(f"{'✅' if success else '❌'} 记录 {agent_id} 决策: {success}")
        
        # 测试3：记录周期性能
        logger.info("测试3: 记录周期性能...")
        performance_metrics = WeeklyPerformanceMetrics(
            week_number=1,
            start_date="2025-01-01",
            end_date="2025-01-07",
            total_return=5.2,
            sharpe_ratio=1.8,
            max_drawdown=2.1,
            volatility=12.5,
            win_rate=0.67,
            total_trades=15,
            agent_contributions={"TRA": 0.02, "NSA": 0.015, "FSA": 0.01},
            risk_metrics={"var": 1.5, "es": 2.0},
            metadata={"test_performance": True}
        )
        
        success = storage_manager.record_weekly_performance(performance_metrics, "baseline")
        logger.info(f"{'✅' if success else '❌'} 记录周期性能: {success}")
        
        # 测试4：完成周期记录
        logger.info("测试4: 完成周期记录...")
        result = storage_manager.finish_weekly_cycle({"test_summary": "baseline_complete"})
        logger.info(f"{'✅' if result['success'] else '❌'} 完成周期记录: {result['success']}")
        
        # 测试5：查询周期数据
        logger.info("测试5: 查询周期数据...")
        weekly_data = storage_manager.get_weekly_data(1, "baseline_operation")
        if "error" not in weekly_data:
            logger.info(f"✅ 查询到周期数据: {len(weekly_data.get('agent_decisions', []))} 个决策记录")
        else:
            logger.error(f"❌ 查询周期数据失败: {weekly_data['error']}")
        
        logger.info("✅ 周期性交易存储管理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 周期性交易存储管理器测试失败: {e}")
        return False

def test_opro_trading_data_integration():
    """测试OPRO交易数据集成"""
    logger = setup_test_logger()
    logger.info("=" * 60)
    logger.info("🧪 开始测试OPRO交易数据集成")
    logger.info("=" * 60)
    
    try:
        # 初始化集成器
        integration = OPROTradingDataIntegration(
            base_data_dir="data/trading_test",
            config={"test_mode": True},
            logger=logger
        )
        
        # 测试1：开始周期集成
        logger.info("测试1: 开始周期集成...")
        week_id = integration.start_weekly_cycle_integration(
            week_number=2,
            week_type="ab_testing",
            start_date="2025-01-08",
            end_date="2025-01-14",
            target_agents=["TRA", "NSA", "FSA"],
            metadata={"test": "ab_testing_week"}
        )
        logger.info(f"✅ 周期集成ID: {week_id}")
        
        # 测试2：记录每日交易决策
        logger.info("测试2: 记录每日交易决策...")
        test_daily_results = {
            "success": True,
            "execution_time": 120.5,
            "assessment_result": {
                "shapley_values": {
                    "TRA": 0.025,
                    "NSA": -0.015,
                    "FSA": 0.008
                },
                "agent_decisions": {
                    "TRA": {"action": "buy", "confidence": 0.85},
                    "NSA": {"action": "sell", "confidence": 0.75},
                    "FSA": {"action": "hold", "confidence": 0.65}
                },
                "market_conditions": {
                    "volatility": "high",
                    "trend": "bullish",
                    "volume": "normal"
                }
            }
        }
        
        success = integration.record_daily_trading_decisions(
            daily_results=test_daily_results,
            trading_date="2025-01-08",
            track_type="optimized"
        )
        logger.info(f"{'✅' if success else '❌'} 记录每日交易决策: {success}")
        
        # 测试3：记录周期性能集成
        logger.info("测试3: 记录周期性能集成...")
        test_weekly_results = {
            "trading_statistics": {
                "total_trades": 20,
                "win_rate": 0.75
            },
            "shapley_results": {
                "shapley_values": {
                    "TRA": 0.03,
                    "NSA": -0.01,
                    "FSA": 0.015
                }
            }
        }
        
        success = integration.record_weekly_performance_integration(
            weekly_results=test_weekly_results,
            track_type="optimized"
        )
        logger.info(f"{'✅' if success else '❌'} 记录周期性能集成: {success}")
        
        # 测试4：完成周期集成
        logger.info("测试4: 完成周期集成...")
        result = integration.finish_weekly_cycle_integration(
            weekly_summary={"status": "ab_testing_complete", "test": True}
        )
        logger.info(f"{'✅' if result['success'] else '❌'} 完成周期集成: {result['success']}")
        
        # 测试5：获取获胜实验数据
        logger.info("测试5: 获取获胜实验数据...")
        winning_data = integration.get_winning_experiment_data_integration(2)
        if winning_data:
            logger.info(f"✅ 获取获胜实验数据: {len(winning_data.get('target_agents', []))} 个目标智能体")
        else:
            logger.warning("⚠️ 未获取到获胜实验数据（可能是正常的，因为这是测试数据）")
        
        logger.info("✅ OPRO交易数据集成测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ OPRO交易数据集成测试失败: {e}")
        return False

def test_data_storage_structure():
    """测试数据存储结构"""
    logger = setup_test_logger()
    logger.info("=" * 60)
    logger.info("🧪 测试数据存储结构")
    logger.info("=" * 60)
    
    try:
        test_data_dir = Path("data/trading_test")
        
        # 检查目录结构
        expected_dirs = [
            "weekly_records",
            "weekly_records/week_1_baseline_operation",
            "weekly_records/week_2_ab_testing"
        ]
        
        for dir_path in expected_dirs:
            full_path = test_data_dir / dir_path
            if full_path.exists():
                logger.info(f"✅ 目录存在: {dir_path}")
            else:
                logger.warning(f"⚠️ 目录不存在: {dir_path}")
        
        # 检查数据库文件
        db_file = test_data_dir / "weekly_trading_data.db"
        if db_file.exists():
            logger.info(f"✅ 数据库文件存在: {db_file}")
        else:
            logger.warning(f"⚠️ 数据库文件不存在: {db_file}")
        
        # 检查JSON文件
        json_files = list(test_data_dir.rglob("*.json"))
        logger.info(f"📄 找到 {len(json_files)} 个JSON文件:")
        for json_file in json_files[:10]:  # 只显示前10个
            relative_path = json_file.relative_to(test_data_dir)
            logger.info(f"   - {relative_path}")
        
        logger.info("✅ 数据存储结构测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据存储结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = setup_test_logger()
    logger.info("🚀 开始交易存储系统综合测试")
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("周期性交易存储管理器", test_weekly_trading_storage_manager),
        ("OPRO交易数据集成", test_opro_trading_data_integration),
        ("数据存储结构", test_data_storage_structure)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            logger.info(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            logger.error(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试总结")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\n📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！交易存储系统功能正常")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
