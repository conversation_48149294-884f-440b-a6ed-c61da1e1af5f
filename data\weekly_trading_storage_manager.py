#!/usr/bin/env python3
"""
周期性交易记录存储管理器

专门为OPRO系统的周期性交易活动提供详细的数据存储功能，
包括每周交易记录、A/B测试数据分离存储、性能指标本地存储等。

创建时间: 2025-07-06
作者: OPRO系统
"""

import os
import json
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
import uuid
import shutil

@dataclass
class AgentDecisionRecord:
    """智能体决策记录"""
    agent_id: str
    decision_type: str  # "buy", "sell", "hold"
    decision_reason: str
    analysis_process: str
    confidence_score: float
    timestamp: str
    market_conditions: Dict[str, Any]
    input_data: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class WeeklyPerformanceMetrics:
    """周期性能指标"""
    week_number: int
    start_date: str
    end_date: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    win_rate: float
    total_trades: int
    agent_contributions: Dict[str, float]
    risk_metrics: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class ABTestTrackData:
    """A/B测试轨道数据"""
    track_type: str  # "original" or "optimized"
    agent_id: str
    week_number: int
    decisions: List[AgentDecisionRecord]
    performance_metrics: WeeklyPerformanceMetrics
    prompt_version: str
    prompt_hash: str
    metadata: Dict[str, Any]

class WeeklyTradingStorageManager:
    """
    周期性交易记录存储管理器
    
    为OPRO系统提供专门的周期性交易数据存储功能，
    支持基线运行、A/B测试、性能指标等数据的结构化存储
    """
    
    def __init__(self, 
                 base_data_dir: str = "data/trading",
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化周期性交易存储管理器
        
        参数:
            base_data_dir: 基础数据目录
            config: 配置字典
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        self.base_data_dir = Path(base_data_dir)
        self.config = config or {}
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 当前活跃的周期
        self.current_week_number = None
        self.current_week_type = None  # "baseline_operation" or "ab_testing"
        self.current_week_dir = None
        
        # A/B测试数据缓存
        self.ab_test_data_cache = {}
        
        # 初始化存储结构
        self._initialize_storage_structure()
        
        # 初始化数据库
        self._initialize_database()
        
        self.logger.info(f"周期性交易存储管理器初始化完成，数据目录: {self.base_data_dir}")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.WeeklyTradingStorageManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _initialize_storage_structure(self):
        """初始化存储目录结构"""
        try:
            # 创建主要目录
            directories = [
                self.base_data_dir,
                self.base_data_dir / "weekly_records",
                self.base_data_dir / "ab_test_data",
                self.base_data_dir / "performance_metrics",
                self.base_data_dir / "trading_summaries",
                self.base_data_dir / "agent_decisions",
                self.base_data_dir / "backups"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            self.logger.info("存储目录结构初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化存储结构失败: {e}")
            raise
    
    def _initialize_database(self):
        """初始化SQLite数据库"""
        try:
            db_path = self.base_data_dir / "weekly_trading_data.db"
            
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                
                # 创建周期记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS weekly_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        week_number INTEGER NOT NULL,
                        week_type TEXT NOT NULL,
                        start_date TEXT NOT NULL,
                        end_date TEXT NOT NULL,
                        status TEXT DEFAULT 'active',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}'
                    )
                """)
                
                # 创建智能体决策表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS agent_decisions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        week_number INTEGER NOT NULL,
                        agent_id TEXT NOT NULL,
                        decision_type TEXT NOT NULL,
                        decision_reason TEXT,
                        analysis_process TEXT,
                        confidence_score REAL,
                        timestamp TEXT NOT NULL,
                        track_type TEXT DEFAULT 'baseline',
                        market_conditions TEXT DEFAULT '{}',
                        input_data TEXT DEFAULT '{}',
                        metadata TEXT DEFAULT '{}'
                    )
                """)
                
                # 创建性能指标表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        week_number INTEGER NOT NULL,
                        track_type TEXT DEFAULT 'baseline',
                        total_return REAL,
                        sharpe_ratio REAL,
                        max_drawdown REAL,
                        volatility REAL,
                        win_rate REAL,
                        total_trades INTEGER,
                        agent_contributions TEXT DEFAULT '{}',
                        risk_metrics TEXT DEFAULT '{}',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}'
                    )
                """)
                
                # 创建A/B测试数据表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ab_test_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        week_number INTEGER NOT NULL,
                        agent_id TEXT NOT NULL,
                        track_type TEXT NOT NULL,
                        prompt_version TEXT,
                        prompt_hash TEXT,
                        performance_data TEXT DEFAULT '{}',
                        decisions_data TEXT DEFAULT '{}',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT DEFAULT '{}'
                    )
                """)
                
                conn.commit()
            
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def start_weekly_cycle(self, 
                          week_number: int,
                          week_type: str,
                          start_date: str,
                          end_date: str,
                          metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        开始新的周期记录
        
        参数:
            week_number: 周次编号
            week_type: 周期类型 ("baseline_operation" 或 "ab_testing")
            start_date: 开始日期
            end_date: 结束日期
            metadata: 元数据
            
        返回:
            周期ID
        """
        with self._lock:
            try:
                self.current_week_number = week_number
                self.current_week_type = week_type
                
                # 创建周期目录
                week_dir_name = f"week_{week_number}_{week_type}"
                self.current_week_dir = self.base_data_dir / "weekly_records" / week_dir_name
                self.current_week_dir.mkdir(parents=True, exist_ok=True)
                
                # 创建子目录
                subdirs = ["agent_decisions", "performance_data", "trading_logs", "analysis_results"]
                if week_type == "ab_testing":
                    subdirs.extend(["original_track", "optimized_track", "comparison_data"])
                
                for subdir in subdirs:
                    (self.current_week_dir / subdir).mkdir(exist_ok=True)
                
                # 记录到数据库
                db_path = self.base_data_dir / "weekly_trading_data.db"
                with sqlite3.connect(str(db_path)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT INTO weekly_records 
                        (week_number, week_type, start_date, end_date, metadata)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        week_number,
                        week_type,
                        start_date,
                        end_date,
                        json.dumps(metadata or {}, ensure_ascii=False)
                    ))
                    conn.commit()
                
                # 创建周期配置文件
                week_config = {
                    "week_number": week_number,
                    "week_type": week_type,
                    "start_date": start_date,
                    "end_date": end_date,
                    "created_at": datetime.now().isoformat(),
                    "metadata": metadata or {}
                }
                
                config_file = self.current_week_dir / "week_config.json"
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(week_config, f, ensure_ascii=False, indent=2)
                
                week_id = f"week_{week_number}_{week_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                self.logger.info(f"开始周期记录: {week_id}")
                return week_id
                
            except Exception as e:
                self.logger.error(f"开始周期记录失败: {e}")
                raise
    
    def record_agent_decision(self, decision_record: AgentDecisionRecord, track_type: str = "baseline") -> bool:
        """
        记录智能体决策
        
        参数:
            decision_record: 决策记录
            track_type: 轨道类型 ("baseline", "original", "optimized")
            
        返回:
            是否记录成功
        """
        if not self.current_week_dir:
            self.logger.error("未开始周期记录，请先调用start_weekly_cycle()")
            return False
        
        with self._lock:
            try:
                # 保存到文件
                decisions_dir = self.current_week_dir / "agent_decisions"
                if track_type != "baseline":
                    decisions_dir = self.current_week_dir / f"{track_type}_track" / "decisions"
                    decisions_dir.mkdir(parents=True, exist_ok=True)
                
                decision_file = decisions_dir / f"{decision_record.agent_id}_decisions.json"
                
                # 读取现有决策记录
                decisions = []
                if decision_file.exists():
                    with open(decision_file, 'r', encoding='utf-8') as f:
                        decisions = json.load(f)
                
                # 添加新决策
                decisions.append(asdict(decision_record))
                
                # 保存更新的决策记录
                with open(decision_file, 'w', encoding='utf-8') as f:
                    json.dump(decisions, f, ensure_ascii=False, indent=2)
                
                # 保存到数据库
                db_path = self.base_data_dir / "weekly_trading_data.db"
                with sqlite3.connect(str(db_path)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT INTO agent_decisions 
                        (week_number, agent_id, decision_type, decision_reason, analysis_process,
                         confidence_score, timestamp, track_type, market_conditions, input_data, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        self.current_week_number,
                        decision_record.agent_id,
                        decision_record.decision_type,
                        decision_record.decision_reason,
                        decision_record.analysis_process,
                        decision_record.confidence_score,
                        decision_record.timestamp,
                        track_type,
                        json.dumps(decision_record.market_conditions, ensure_ascii=False),
                        json.dumps(decision_record.input_data, ensure_ascii=False),
                        json.dumps(decision_record.metadata, ensure_ascii=False)
                    ))
                    conn.commit()
                
                self.logger.debug(f"记录智能体决策: {decision_record.agent_id} - {decision_record.decision_type}")
                return True

            except Exception as e:
                self.logger.error(f"记录智能体决策失败: {e}")
                return False

    def record_weekly_performance(self, performance_metrics: WeeklyPerformanceMetrics, track_type: str = "baseline") -> bool:
        """
        记录周期性能指标

        参数:
            performance_metrics: 性能指标
            track_type: 轨道类型 ("baseline", "original", "optimized")

        返回:
            是否记录成功
        """
        if not self.current_week_dir:
            self.logger.error("未开始周期记录，请先调用start_weekly_cycle()")
            return False

        with self._lock:
            try:
                # 保存到文件
                performance_dir = self.current_week_dir / "performance_data"
                if track_type != "baseline":
                    performance_dir = self.current_week_dir / f"{track_type}_track" / "performance"
                    performance_dir.mkdir(parents=True, exist_ok=True)

                performance_file = performance_dir / f"performance_metrics_{track_type}.json"

                # 保存性能指标
                with open(performance_file, 'w', encoding='utf-8') as f:
                    json.dump(asdict(performance_metrics), f, ensure_ascii=False, indent=2)

                # 保存到数据库
                db_path = self.base_data_dir / "weekly_trading_data.db"
                with sqlite3.connect(str(db_path)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT INTO performance_metrics
                        (week_number, track_type, total_return, sharpe_ratio, max_drawdown,
                         volatility, win_rate, total_trades, agent_contributions, risk_metrics, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        performance_metrics.week_number,
                        track_type,
                        performance_metrics.total_return,
                        performance_metrics.sharpe_ratio,
                        performance_metrics.max_drawdown,
                        performance_metrics.volatility,
                        performance_metrics.win_rate,
                        performance_metrics.total_trades,
                        json.dumps(performance_metrics.agent_contributions, ensure_ascii=False),
                        json.dumps(performance_metrics.risk_metrics, ensure_ascii=False),
                        json.dumps(performance_metrics.metadata, ensure_ascii=False)
                    ))
                    conn.commit()

                self.logger.info(f"记录周期性能指标: 第{performance_metrics.week_number}周 - {track_type}")
                return True

            except Exception as e:
                self.logger.error(f"记录周期性能指标失败: {e}")
                return False

    def record_ab_test_data(self, ab_test_data: ABTestTrackData) -> bool:
        """
        记录A/B测试数据

        参数:
            ab_test_data: A/B测试轨道数据

        返回:
            是否记录成功
        """
        if not self.current_week_dir:
            self.logger.error("未开始周期记录，请先调用start_weekly_cycle()")
            return False

        if self.current_week_type != "ab_testing":
            self.logger.error("当前周期不是A/B测试周期")
            return False

        with self._lock:
            try:
                # 保存到轨道特定目录
                track_dir = self.current_week_dir / f"{ab_test_data.track_type}_track"
                track_dir.mkdir(parents=True, exist_ok=True)

                # 保存A/B测试数据
                ab_test_file = track_dir / f"{ab_test_data.agent_id}_ab_test_data.json"
                with open(ab_test_file, 'w', encoding='utf-8') as f:
                    json.dump(asdict(ab_test_data), f, ensure_ascii=False, indent=2)

                # 保存到数据库
                db_path = self.base_data_dir / "weekly_trading_data.db"
                with sqlite3.connect(str(db_path)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT INTO ab_test_data
                        (week_number, agent_id, track_type, prompt_version, prompt_hash,
                         performance_data, decisions_data, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        ab_test_data.week_number,
                        ab_test_data.agent_id,
                        ab_test_data.track_type,
                        ab_test_data.prompt_version,
                        ab_test_data.prompt_hash,
                        json.dumps(asdict(ab_test_data.performance_metrics), ensure_ascii=False),
                        json.dumps([asdict(d) for d in ab_test_data.decisions], ensure_ascii=False),
                        json.dumps(ab_test_data.metadata, ensure_ascii=False)
                    ))
                    conn.commit()

                # 缓存A/B测试数据
                cache_key = f"{ab_test_data.week_number}_{ab_test_data.agent_id}_{ab_test_data.track_type}"
                self.ab_test_data_cache[cache_key] = ab_test_data

                self.logger.info(f"记录A/B测试数据: {ab_test_data.agent_id} - {ab_test_data.track_type}")
                return True

            except Exception as e:
                self.logger.error(f"记录A/B测试数据失败: {e}")
                return False

    def create_ab_test_comparison(self, week_number: int, agent_ids: List[str]) -> Dict[str, Any]:
        """
        创建A/B测试对比数据

        参数:
            week_number: 周次编号
            agent_ids: 智能体ID列表

        返回:
            对比数据字典
        """
        try:
            comparison_data = {
                "week_number": week_number,
                "comparison_timestamp": datetime.now().isoformat(),
                "agent_comparisons": {},
                "overall_comparison": {},
                "statistical_analysis": {}
            }

            for agent_id in agent_ids:
                # 获取原始轨道和优化轨道数据
                original_key = f"{week_number}_{agent_id}_original"
                optimized_key = f"{week_number}_{agent_id}_optimized"

                original_data = self.ab_test_data_cache.get(original_key)
                optimized_data = self.ab_test_data_cache.get(optimized_key)

                if original_data and optimized_data:
                    agent_comparison = self._compare_ab_test_tracks(original_data, optimized_data)
                    comparison_data["agent_comparisons"][agent_id] = agent_comparison

            # 计算整体对比
            if comparison_data["agent_comparisons"]:
                comparison_data["overall_comparison"] = self._calculate_overall_comparison(
                    comparison_data["agent_comparisons"]
                )

            # 保存对比数据
            if self.current_week_dir:
                comparison_file = self.current_week_dir / "comparison_data" / "ab_test_comparison.json"
                comparison_file.parent.mkdir(parents=True, exist_ok=True)

                with open(comparison_file, 'w', encoding='utf-8') as f:
                    json.dump(comparison_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"创建A/B测试对比数据: 第{week_number}周")
            return comparison_data

        except Exception as e:
            self.logger.error(f"创建A/B测试对比数据失败: {e}")
            return {}

    def _compare_ab_test_tracks(self, original_data: ABTestTrackData, optimized_data: ABTestTrackData) -> Dict[str, Any]:
        """比较A/B测试轨道数据"""
        try:
            original_perf = original_data.performance_metrics
            optimized_perf = optimized_data.performance_metrics

            comparison = {
                "agent_id": original_data.agent_id,
                "performance_comparison": {
                    "return_improvement": optimized_perf.total_return - original_perf.total_return,
                    "sharpe_improvement": optimized_perf.sharpe_ratio - original_perf.sharpe_ratio,
                    "drawdown_improvement": original_perf.max_drawdown - optimized_perf.max_drawdown,
                    "volatility_change": optimized_perf.volatility - original_perf.volatility,
                    "win_rate_improvement": optimized_perf.win_rate - original_perf.win_rate
                },
                "decision_analysis": {
                    "original_decisions": len(original_data.decisions),
                    "optimized_decisions": len(optimized_data.decisions),
                    "decision_type_changes": self._analyze_decision_changes(
                        original_data.decisions, optimized_data.decisions
                    )
                },
                "recommendation": self._generate_track_recommendation(original_perf, optimized_perf)
            }

            return comparison

        except Exception as e:
            self.logger.error(f"比较A/B测试轨道失败: {e}")
            return {}

    def _analyze_decision_changes(self, original_decisions: List[AgentDecisionRecord],
                                optimized_decisions: List[AgentDecisionRecord]) -> Dict[str, Any]:
        """分析决策变化"""
        try:
            original_types = [d.decision_type for d in original_decisions]
            optimized_types = [d.decision_type for d in optimized_decisions]

            original_counts = {
                "buy": original_types.count("buy"),
                "sell": original_types.count("sell"),
                "hold": original_types.count("hold")
            }

            optimized_counts = {
                "buy": optimized_types.count("buy"),
                "sell": optimized_types.count("sell"),
                "hold": optimized_types.count("hold")
            }

            return {
                "original_distribution": original_counts,
                "optimized_distribution": optimized_counts,
                "changes": {
                    "buy_change": optimized_counts["buy"] - original_counts["buy"],
                    "sell_change": optimized_counts["sell"] - original_counts["sell"],
                    "hold_change": optimized_counts["hold"] - original_counts["hold"]
                }
            }

        except Exception as e:
            self.logger.error(f"分析决策变化失败: {e}")
            return {}

    def _generate_track_recommendation(self, original_perf: WeeklyPerformanceMetrics,
                                     optimized_perf: WeeklyPerformanceMetrics) -> str:
        """生成轨道推荐"""
        try:
            # 简单的推荐逻辑：基于夏普率和收益率
            if optimized_perf.sharpe_ratio > original_perf.sharpe_ratio and optimized_perf.total_return > original_perf.total_return:
                return "optimized"
            elif optimized_perf.sharpe_ratio > original_perf.sharpe_ratio:
                return "optimized"
            elif optimized_perf.total_return > original_perf.total_return * 1.1:  # 收益率显著提升
                return "optimized"
            else:
                return "original"

        except Exception as e:
            self.logger.error(f"生成轨道推荐失败: {e}")
            return "original"

    def _calculate_overall_comparison(self, agent_comparisons: Dict[str, Any]) -> Dict[str, Any]:
        """计算整体对比"""
        try:
            if not agent_comparisons:
                return {}

            total_agents = len(agent_comparisons)
            optimized_recommended = sum(1 for comp in agent_comparisons.values()
                                      if comp.get("recommendation") == "optimized")

            avg_return_improvement = sum(comp["performance_comparison"]["return_improvement"]
                                       for comp in agent_comparisons.values()) / total_agents

            avg_sharpe_improvement = sum(comp["performance_comparison"]["sharpe_improvement"]
                                       for comp in agent_comparisons.values()) / total_agents

            return {
                "total_agents": total_agents,
                "optimized_recommended_count": optimized_recommended,
                "optimized_recommendation_rate": optimized_recommended / total_agents,
                "average_return_improvement": avg_return_improvement,
                "average_sharpe_improvement": avg_sharpe_improvement,
                "overall_recommendation": "optimized" if optimized_recommended > total_agents / 2 else "original"
            }

        except Exception as e:
            self.logger.error(f"计算整体对比失败: {e}")
            return {}

    def finish_weekly_cycle(self, summary_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        完成周期记录

        参数:
            summary_data: 周期总结数据

        返回:
            周期完成结果
        """
        if not self.current_week_dir:
            self.logger.error("未开始周期记录")
            return {"success": False, "error": "未开始周期记录"}

        with self._lock:
            try:
                # 创建周期总结
                trading_summary = {
                    "week_number": self.current_week_number,
                    "week_type": self.current_week_type,
                    "completion_timestamp": datetime.now().isoformat(),
                    "summary_data": summary_data or {},
                    "data_statistics": self._calculate_week_statistics(),
                    "files_created": self._list_created_files(),
                    "status": "completed"
                }

                # 保存周期总结
                summary_file = self.current_week_dir / "trading_summary.json"
                with open(summary_file, 'w', encoding='utf-8') as f:
                    json.dump(trading_summary, f, ensure_ascii=False, indent=2)

                # 更新数据库状态
                db_path = self.base_data_dir / "weekly_trading_data.db"
                with sqlite3.connect(str(db_path)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE weekly_records
                        SET status = 'completed'
                        WHERE week_number = ? AND week_type = ?
                    """, (self.current_week_number, self.current_week_type))
                    conn.commit()

                # 如果是A/B测试周期，创建对比数据
                if self.current_week_type == "ab_testing" and self.current_week_number is not None:
                    agent_ids = list(set(key.split('_')[1] for key in self.ab_test_data_cache.keys()
                                       if key.startswith(f"{self.current_week_number}_")))
                    if agent_ids:
                        comparison_data = self.create_ab_test_comparison(self.current_week_number, agent_ids)
                        trading_summary["ab_test_comparison"] = comparison_data

                # 创建备份
                self._create_week_backup()

                week_result = {
                    "success": True,
                    "week_number": self.current_week_number,
                    "week_type": self.current_week_type,
                    "week_directory": str(self.current_week_dir),
                    "trading_summary": trading_summary
                }

                self.logger.info(f"完成周期记录: 第{self.current_week_number}周 - {self.current_week_type}")

                # 重置当前周期状态
                self.current_week_number = None
                self.current_week_type = None
                self.current_week_dir = None

                return week_result

            except Exception as e:
                self.logger.error(f"完成周期记录失败: {e}")
                return {"success": False, "error": str(e)}

    def _calculate_week_statistics(self) -> Dict[str, Any]:
        """计算周期统计信息"""
        try:
            stats = {
                "total_agent_decisions": 0,
                "decision_type_distribution": {"buy": 0, "sell": 0, "hold": 0},
                "agents_with_data": [],
                "performance_metrics_recorded": False,
                "ab_test_tracks": []
            }

            if not self.current_week_dir:
                return stats

            # 统计智能体决策
            decisions_dir = self.current_week_dir / "agent_decisions"
            if decisions_dir.exists():
                for decision_file in decisions_dir.glob("*_decisions.json"):
                    agent_id = decision_file.stem.replace("_decisions", "")
                    stats["agents_with_data"].append(agent_id)

                    with open(decision_file, 'r', encoding='utf-8') as f:
                        decisions = json.load(f)
                        stats["total_agent_decisions"] += len(decisions)

                        for decision in decisions:
                            decision_type = decision.get("decision_type", "hold")
                            stats["decision_type_distribution"][decision_type] += 1

            # 检查性能指标
            performance_dir = self.current_week_dir / "performance_data"
            if performance_dir.exists() and list(performance_dir.glob("*.json")):
                stats["performance_metrics_recorded"] = True

            # 检查A/B测试轨道
            if self.current_week_type == "ab_testing":
                for track_type in ["original", "optimized"]:
                    track_dir = self.current_week_dir / f"{track_type}_track"
                    if track_dir.exists():
                        stats["ab_test_tracks"].append(track_type)

            return stats

        except Exception as e:
            self.logger.error(f"计算周期统计失败: {e}")
            return {}

    def _list_created_files(self) -> List[str]:
        """列出创建的文件"""
        try:
            files = []
            if self.current_week_dir and self.current_week_dir.exists():
                for file_path in self.current_week_dir.rglob("*"):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.current_week_dir)
                        files.append(str(relative_path))
            return files

        except Exception as e:
            self.logger.error(f"列出创建文件失败: {e}")
            return []

    def _create_week_backup(self):
        """创建周期备份"""
        try:
            if not self.current_week_dir:
                return

            backup_dir = self.base_data_dir / "backups"
            backup_name = f"week_{self.current_week_number}_{self.current_week_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_path = backup_dir / backup_name

            shutil.copytree(self.current_week_dir, backup_path)
            self.logger.info(f"创建周期备份: {backup_path}")

        except Exception as e:
            self.logger.error(f"创建周期备份失败: {e}")

    def get_weekly_data(self, week_number: int, week_type: Optional[str] = None) -> Dict[str, Any]:
        """
        获取周期数据

        参数:
            week_number: 周次编号
            week_type: 周期类型（可选）

        返回:
            周期数据字典
        """
        try:
            # 从数据库查询
            db_path = self.base_data_dir / "weekly_trading_data.db"
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()

                # 查询周期记录
                if week_type:
                    cursor.execute("""
                        SELECT * FROM weekly_records
                        WHERE week_number = ? AND week_type = ?
                    """, (week_number, week_type))
                else:
                    cursor.execute("""
                        SELECT * FROM weekly_records
                        WHERE week_number = ?
                    """, (week_number,))

                week_records = cursor.fetchall()
                if not week_records:
                    return {"error": f"未找到第{week_number}周的数据"}

                # 查询智能体决策
                cursor.execute("""
                    SELECT * FROM agent_decisions
                    WHERE week_number = ?
                """, (week_number,))
                decisions = cursor.fetchall()

                # 查询性能指标
                cursor.execute("""
                    SELECT * FROM performance_metrics
                    WHERE week_number = ?
                """, (week_number,))
                performance = cursor.fetchall()

                # 查询A/B测试数据
                cursor.execute("""
                    SELECT * FROM ab_test_data
                    WHERE week_number = ?
                """, (week_number,))
                ab_test_data = cursor.fetchall()

            return {
                "week_records": week_records,
                "agent_decisions": decisions,
                "performance_metrics": performance,
                "ab_test_data": ab_test_data
            }

        except Exception as e:
            self.logger.error(f"获取周期数据失败: {e}")
            return {"error": str(e)}

    def get_winning_experiment_data(self, week_number: int) -> Dict[str, Any]:
        """
        获取获胜实验数据（专门为迭代Shapley计算提供）

        参数:
            week_number: 周次编号

        返回:
            获胜实验数据字典
        """
        try:
            # 查找A/B测试周期的对比数据
            week_dir = self.base_data_dir / "weekly_records" / f"week_{week_number}_ab_testing"
            comparison_file = week_dir / "comparison_data" / "ab_test_comparison.json"

            if not comparison_file.exists():
                self.logger.warning(f"第{week_number}周没有A/B测试对比数据")
                return {}

            with open(comparison_file, 'r', encoding='utf-8') as f:
                comparison_data = json.load(f)

            # 提取获胜实验数据
            winning_data = {
                "week_number": week_number,
                "ab_test_data": comparison_data,
                "performance_comparison": comparison_data.get("overall_comparison", {}),
                "prompt_selection_result": {
                    "winning_prompts": {},
                    "agent_selections": {}
                },
                "target_agents": list(comparison_data.get("agent_comparisons", {}).keys())
            }

            # 构建获胜提示词信息
            for agent_id, agent_comp in comparison_data.get("agent_comparisons", {}).items():
                recommendation = agent_comp.get("recommendation", "original")
                winning_data["prompt_selection_result"]["winning_prompts"][agent_id] = recommendation
                winning_data["prompt_selection_result"]["agent_selections"][agent_id] = {
                    "selected_prompt": recommendation,
                    "reason": f"基于性能对比选择{recommendation}提示词"
                }

            self.logger.info(f"获取第{week_number}周获胜实验数据成功")
            return winning_data

        except Exception as e:
            self.logger.error(f"获取获胜实验数据失败: {e}")
            return {}

    def export_weekly_summary(self, week_number: int, export_format: str = "json") -> str:
        """
        导出周期总结

        参数:
            week_number: 周次编号
            export_format: 导出格式 ("json", "csv")

        返回:
            导出文件路径
        """
        try:
            weekly_data = self.get_weekly_data(week_number)
            if "error" in weekly_data:
                raise ValueError(weekly_data["error"])

            export_dir = self.base_data_dir / "exports"
            export_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            if export_format == "json":
                export_file = export_dir / f"week_{week_number}_summary_{timestamp}.json"
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(weekly_data, f, ensure_ascii=False, indent=2)

            elif export_format == "csv":
                import pandas as pd
                export_file = export_dir / f"week_{week_number}_summary_{timestamp}.csv"

                # 转换为DataFrame并导出
                decisions_df = pd.DataFrame(weekly_data["agent_decisions"])
                decisions_df.to_csv(export_file, index=False, encoding='utf-8')

            else:
                raise ValueError(f"不支持的导出格式: {export_format}")

            self.logger.info(f"导出周期总结: {export_file}")
            return str(export_file)

        except Exception as e:
            self.logger.error(f"导出周期总结失败: {e}")
            return ""
