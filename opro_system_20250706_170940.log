2025-07-06 17:09:40,869 - __main__ - INFO - ====================================================================================================
2025-07-06 17:09:40,869 - __main__ - INFO - OPRO系统启动
2025-07-06 17:09:40,869 - __main__ - INFO - ====================================================================================================
2025-07-06 17:09:40,869 - __main__ - INFO - 运行模式: integrated
2025-07-06 17:09:40,869 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 17:09:40,869 - __main__ - INFO - OPRO启用: True
2025-07-06 17:09:40,869 - __main__ - INFO - 数据存储启用: True
2025-07-06 17:09:40,869 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 17:09:40,870 - __main__ - INFO - 初始化系统...
2025-07-06 17:09:40,870 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 17:09:40,870 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 17:09:40,871 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 17:09:40,871 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 17:09:40,871 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 17:09:40,877 - __main__ - INFO - 数据库初始化完成
2025-07-06 17:09:40,878 - __main__ - INFO - 自动备份线程已启动
2025-07-06 17:09:40,878 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:09:40,878 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 17:09:40,878 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 17:09:40,884 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 17:09:40,884 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 17:09:40,884 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 17:09:40,885 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:09:40,885 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:09:40,885 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 17:09:40,886 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 17:09:40,887 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 17:09:40,887 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 17:09:40,887 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 17:09:40,887 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 17:09:40,887 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 17:09:40,893 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:09:40,894 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:09:40,894 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:09:40,894 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:09:40,894 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 17:09:40,895 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 17:09:40,895 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 17:09:40,895 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 17:09:40,895 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 17:09:40,896 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 17:09:40,898 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 17:09:40,898 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:09:40,903 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 17:09:40,903 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 17:09:40,904 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 17:09:40,905 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 17:09:40,906 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 17:09:41,037 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 17:09:41,038 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 17:09:41,157 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 17:09:41,158 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 17:09:41,280 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 17:09:41,281 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 17:09:41,382 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 17:09:41,382 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 17:09:41,382 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 17:09:41,384 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 17:09:41,384 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 17:09:41,384 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:09:41,391 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:09:41,392 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:09:41,392 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:09:41,392 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:09:41,392 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:09:41,393 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:09:41,393 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:09:41,393 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:09:41,394 - __main__ - INFO - 加载历史数据完成: 2 个实验记录
2025-07-06 17:09:41,394 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:09:41,394 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:09:41,395 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:09:41,395 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 17:09:41,395 - __main__ - INFO - 系统初始化完成
2025-07-06 17:09:41,395 - __main__ - INFO - ================================================================================
2025-07-06 17:09:41,395 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 17:09:41,396 - __main__ - INFO - ================================================================================
2025-07-06 17:09:41,396 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-03-31
2025-07-06 17:09:41,396 - __main__ - INFO - ====================================================================================================
2025-07-06 17:09:41,396 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 17:09:41,396 - __main__ - INFO - ====================================================================================================
2025-07-06 17:09:41,396 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-03-31
2025-07-06 17:09:41,396 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:09:41,396 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 17:09:41,396 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 17:09:41,398 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-03-31
2025-07-06 17:09:41,398 - __main__ - INFO - 📊 总交易日数: 64
2025-07-06 17:09:41,398 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 17:09:41,398 - __main__ - INFO - 🗓️  最后交易日: 2025-03-31
2025-07-06 17:09:41,398 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 17:09:41,399 - __main__ - INFO - 📊 生成了 9 个7天周期
2025-07-06 17:09:41,399 - __main__ - INFO -    - 基线运行周: 5 个
2025-07-06 17:09:41,399 - __main__ - INFO -    - A/B测试周: 4 个
2025-07-06 17:09:41,399 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 17:09:41,399 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 17:09:41,399 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 17:09:41,399 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 17:09:41,399 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 17:09:41,399 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 17:09:41,400 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 17:09:41,400 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 17:09:41,400 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 17:09:41,400 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 17:09:41,400 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 17:09:41,400 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 17:09:41,400 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 17:09:41,400 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:09:41,401 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:09:41,401 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:09:41,405 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:09:41,405 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:09:41,406 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:09:41,406 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:09:41,406 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:09:41,406 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:09:41,406 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:09:41,407 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.030006
2025-07-06 17:09:41,407 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:09:41,407 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.026732
2025-07-06 17:09:41,407 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:09:41,407 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.065808
2025-07-06 17:09:41,407 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:09:41,407 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.002858
2025-07-06 17:09:41,407 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:09:41,407 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.010258
2025-07-06 17:09:41,407 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:09:41,409 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.036296
2025-07-06 17:09:41,409 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:09:41,409 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.059725
2025-07-06 17:09:41,409 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.231683 = 大联盟值 0.231683
2025-07-06 17:09:41,409 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-06 17:09:41,409 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:09:41,409 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:09:41,409 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:09:41,410 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:09:41,410 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 17:09:41,410 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:09:41,410 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:09:41,412 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:41,412 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:41,412 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-06 17:09:41,417 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000018B13626810>
2025-07-06 17:09:41,417 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-06 17:09:41,417 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:41,418 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-06 17:09:41,418 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:41,418 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-06 17:09:41,418 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-06 17:09:41,418 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000018B1363C650> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-06 17:09:41,602 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000018B13626150>
2025-07-06 17:09:41,602 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:41,602 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:41,602 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:41,604 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:41,604 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:09:42,695 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_170940\\prompts\\BOA\\opt_BOA_20250705_232752_15e1bd3e.json'
2025-07-06 17:09:42,709 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据备份完成: backup_20250706_170940 (3.39 MB)
2025-07-06 17:09:43,156 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据备份完成: backup_20250706_170941 (3.39 MB)
2025-07-06 17:09:44,055 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:09:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=1a0c63d917517929822713048e0088a18beac3e2638ef5ae213fbe39636e08;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617094207645830a5724259'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:09:44,056 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:09:44,057 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:09:44,058 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:09:44,058 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:09:44,058 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:09:44,059 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:09:44,061 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:44,062 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:44,063 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:44,063 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:44,064 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:44,064 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:44,064 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:09:46,385 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:09:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061709448354bab94ca14444'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:09:46,387 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:09:46,387 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:09:46,388 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:09:46,389 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:09:46,389 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:09:46,390 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:09:46,391 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:46,392 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:46,394 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:46,394 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:46,394 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:46,395 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:46,395 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:09:49,061 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:09:49 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061709477753d5e4245e4314'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:09:49,062 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:09:49,062 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:09:49,064 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:09:49,064 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:09:49,065 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:09:49,065 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:09:49,067 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:49,067 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:49,069 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:49,069 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:49,070 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:49,070 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:49,070 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:09:52,784 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:09:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061709494f983cd777d44430'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:09:52,785 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:09:52,785 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:09:52,786 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:09:52,786 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:09:52,787 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:09:52,787 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:09:52,789 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:52,789 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:52,790 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:52,790 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:52,791 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:52,791 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:52,791 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:09:55,961 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:09:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706170953afd42b82e9784918'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:09:55,962 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:09:55,962 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:09:55,963 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:09:55,963 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:09:55,964 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:09:55,964 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:09:55,966 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:55,966 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:55,967 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:55,968 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:55,968 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:55,968 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:55,968 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:09:58,669 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:09:59 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617095685225122dc664b73'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:09:58,671 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:09:58,671 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:09:58,672 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:09:58,672 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:09:58,672 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:09:58,673 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:09:58,674 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:09:58,674 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:09:58,676 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:09:58,677 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:09:58,677 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:09:58,678 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:09:58,678 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:02,230 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706170959fef65175ab284bbc'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:02,231 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:02,232 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:02,232 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:02,232 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:02,232 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:02,234 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:02,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:02,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:02,238 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:02,238 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:02,238 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:02,238 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:02,239 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:04,974 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:05 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171002d4a62ab813e14e1c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:04,975 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:04,976 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:04,977 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:04,977 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:04,978 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:04,978 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:04,980 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:10:04,996 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_171004_eee06c6b
2025-07-06 17:10:04,997 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_171004_eee06c6b
2025-07-06 17:10:04,997 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_171004_eee06c6b
2025-07-06 17:10:04,997 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.816400
2025-07-06 17:10:04,997 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:10:04,998 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:10:04,998 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:10:04,999 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:04,999 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:05,000 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:05,001 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:05,001 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:05,002 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:05,002 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:07,362 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:07 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171005d7872cac5bf649a9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:07,363 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:07,363 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:07,364 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:07,365 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:07,365 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:07,365 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:07,367 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:07,367 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:07,369 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:07,370 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:07,371 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:07,371 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:07,371 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:11,685 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171008a21d040c823942c0'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:11,686 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:11,686 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:11,687 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:11,688 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:11,688 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:11,688 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:11,690 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:11,691 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:11,692 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:11,693 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:11,693 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:11,694 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:11,694 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:17,341 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617101211d5d414df2f49f8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:17,342 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:17,342 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:17,343 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:17,343 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:17,345 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:17,345 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:17,347 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:17,348 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:17,349 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:17,350 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:17,350 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:17,351 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:17,351 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:20,498 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171018d098f656ecf9483e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:20,498 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:20,499 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:20,500 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:20,500 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:20,500 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:20,501 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:20,503 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:20,504 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:20,505 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:20,506 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:20,506 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:20,507 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:20,507 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:24,864 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617102105eaec06bfb54b96'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:24,865 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:24,865 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:24,866 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:24,867 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:24,867 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:24,867 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:24,869 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:24,870 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:24,871 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:24,872 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:24,872 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:24,872 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:24,873 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:29,319 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171025c1993ca5f6734330'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:29,320 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:29,320 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:29,321 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:29,322 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:29,322 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:29,323 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:29,325 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:29,326 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:29,328 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:29,329 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:29,329 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:29,330 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:29,330 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:33,396 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617103023cae6369ea64682'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:33,397 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:33,397 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:33,398 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:33,399 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:33,399 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:33,399 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:33,401 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:33,402 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:33,403 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:33,403 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:33,404 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:33,405 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:33,405 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:39,240 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171034e076a9a8e91c454f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:39,241 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:39,241 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:39,242 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:39,242 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:39,244 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:39,244 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:39,247 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:10:39,255 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_171039_cae4f742
2025-07-06 17:10:39,255 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_171039_cae4f742
2025-07-06 17:10:39,255 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_171039_cae4f742
2025-07-06 17:10:39,256 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.780020
2025-07-06 17:10:39,256 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:10:39,275 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-01
2025-07-06 17:10:39,276 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:10:39,276 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:10:39,276 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 17:10:39,276 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-09
2025-07-06 17:10:39,276 - __main__ - INFO - 🆕 开始第2周 - A/B测试阶段: 2025-01-09
2025-07-06 17:10:39,276 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第1/7天
2025-07-06 17:10:39,276 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 17:10:39,276 - __main__ - INFO - 🔄 开始第 2 周（ab_testing）: 2025-01-10 到 2025-01-20
2025-07-06 17:10:39,276 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-10
2025-07-06 17:10:39,277 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第2/7天
2025-07-06 17:10:39,277 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-13
2025-07-06 17:10:39,277 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第5/7天
2025-07-06 17:10:39,277 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-14
2025-07-06 17:10:39,277 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第6/7天
2025-07-06 17:10:39,277 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-15
2025-07-06 17:10:39,277 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第7/7天
2025-07-06 17:10:39,277 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:10:39,277 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:10:39,277 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:10:39,278 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:10:39,278 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:10:39,278 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:10:39,278 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:10:39,278 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:10:39,278 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_171039
2025-07-06 17:10:39,278 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_171039 - 0 条记录
2025-07-06 17:10:39,279 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_171039
2025-07-06 17:10:39,279 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:10:39,279 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-16
2025-07-06 17:10:39,279 - __main__ - INFO - 🆕 开始第3周 - 基线运行阶段: 2025-01-16
2025-07-06 17:10:39,279 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第1/7天
2025-07-06 17:10:39,279 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-17
2025-07-06 17:10:39,279 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第2/7天
2025-07-06 17:10:39,279 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-20
2025-07-06 17:10:39,280 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第5/7天
2025-07-06 17:10:39,280 - __main__ - INFO - ✅ 第 2 周完成: ab_testing_complete
2025-07-06 17:10:39,280 - __main__ - INFO - 🔬 第 2 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:10:39,280 - __main__ - WARNING - ⚠️  第 2 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:10:39,280 - __main__ - INFO - 🔄 开始第 3 周（baseline_operation）: 2025-01-21 到 2025-01-29
2025-07-06 17:10:39,280 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-21
2025-07-06 17:10:39,280 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第6/7天
2025-07-06 17:10:39,280 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-22
2025-07-06 17:10:39,280 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第7/7天
2025-07-06 17:10:39,280 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:10:39,280 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:10:39,281 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:10:39,284 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:10:39,284 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:10:39,284 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:10:39,284 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:10:39,284 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:10:39,284 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:10:39,284 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:10:39,284 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.030006
2025-07-06 17:10:39,284 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:10:39,284 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.026732
2025-07-06 17:10:39,284 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:10:39,285 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.065808
2025-07-06 17:10:39,285 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:10:39,285 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.002858
2025-07-06 17:10:39,285 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:10:39,285 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.010258
2025-07-06 17:10:39,285 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:10:39,285 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.036296
2025-07-06 17:10:39,286 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:10:39,286 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.059725
2025-07-06 17:10:39,286 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.231683 = 大联盟值 0.231683
2025-07-06 17:10:39,286 - __main__ - INFO - Shapley值计算完成，耗时 0.003s
2025-07-06 17:10:39,287 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:10:39,287 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:10:39,287 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:10:39,287 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:10:39,287 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-16
2025-07-06 17:10:39,287 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:10:39,288 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:10:39,288 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:39,289 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:39,289 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:39,290 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:39,290 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:39,291 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:39,291 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:44,068 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171039e184659d0bc24615'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:44,068 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:44,069 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:44,070 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:44,070 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:44,070 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:44,071 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:44,073 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:44,073 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:44,075 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:44,076 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:44,076 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:44,077 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:44,077 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:47,954 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:48 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061710442f2ab6e723f1410e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:47,955 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:47,955 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:47,956 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:47,956 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:47,957 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:47,957 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:47,959 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:47,959 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:47,961 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:47,961 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:47,962 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:47,963 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:47,963 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:50,492 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:51 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061710482898142aa64b44c6'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:50,492 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:50,493 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:50,494 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:50,495 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:50,495 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:50,496 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:50,497 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:50,497 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:50,498 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:50,498 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:50,499 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:50,500 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:50,500 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:53,363 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171051e313b21d8c4b4573'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:53,364 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:53,365 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:53,365 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:53,365 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:53,366 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:53,366 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:53,370 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:53,370 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:53,372 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:53,372 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:53,373 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:53,373 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:53,373 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:56,127 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:10:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061710544c5045c3a6504598'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:56,128 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:56,128 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:56,128 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:56,128 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:56,128 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:56,128 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:56,129 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:56,129 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:56,130 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:56,131 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:56,131 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:56,131 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:56,131 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:10:59,388 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061710560aedec0b0a0c47e3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:10:59,389 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:10:59,389 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:10:59,389 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:10:59,389 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:10:59,389 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:10:59,389 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:10:59,390 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:10:59,390 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:10:59,391 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:10:59,391 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:10:59,392 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:10:59,392 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:10:59,392 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:04,290 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:04 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061711009c7185207b524608'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:04,291 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:04,292 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:04,292 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:04,292 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:04,293 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:04,293 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:04,296 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:04,296 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:04,298 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:04,298 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:04,298 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:04,298 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:04,299 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:08,111 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:08 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171104a3cfcf9c8bd54ab0'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:08,112 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:08,112 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:08,113 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:08,113 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:08,113 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:08,113 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:08,115 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:11:08,126 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_171108_16f13e43
2025-07-06 17:11:08,126 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_171108_16f13e43
2025-07-06 17:11:08,126 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_171108_16f13e43
2025-07-06 17:11:08,127 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.694979
2025-07-06 17:11:08,127 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:11:08,127 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:11:08,127 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:11:08,128 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:08,128 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:08,129 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:08,129 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:08,130 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:08,130 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:08,130 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:10,853 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061711081a59599bc621402d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:10,854 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:10,855 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:10,855 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:10,856 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:10,856 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:10,856 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:10,858 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:10,858 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:10,860 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:10,860 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:10,861 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:10,862 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:10,862 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:15,141 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171111f6b1a3e156714eb5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:15,142 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:15,142 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:15,143 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:15,144 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:15,144 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:15,144 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:15,146 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:15,146 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:15,148 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:15,148 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:15,149 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:15,150 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:15,150 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:19,290 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171115b73907feb35c411e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:19,291 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:19,292 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:19,292 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:19,292 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:19,292 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:19,293 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:19,295 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:19,295 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:19,297 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:19,297 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:19,298 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:19,299 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:19,299 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:21,663 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:22 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617111923aeb67c814040e3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:21,664 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:21,664 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:21,665 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:21,665 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:21,665 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:21,665 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:21,666 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:21,666 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:21,666 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:21,667 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:21,667 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:21,667 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:21,668 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:26,723 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171122e8be769459074103'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:26,724 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:26,725 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:26,726 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:26,726 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:26,727 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:26,727 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:26,729 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:26,729 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:26,731 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:26,731 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:26,732 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:26,732 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:26,732 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:28,856 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061711273c2aef865b494621'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:28,858 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:28,858 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:28,859 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:28,859 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:28,859 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:28,860 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:28,862 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:28,862 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:28,863 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:28,864 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:28,865 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:28,866 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:28,866 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:32,563 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:33 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171129aa61b1a77e2848b9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:32,563 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:32,564 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:32,564 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:32,565 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:32,565 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:32,566 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:32,567 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:32,568 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:32,569 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:32,570 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:32,570 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:32,571 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:32,571 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:36,373 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061711335abb61f17a5643f8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:36,373 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:36,374 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:36,375 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:36,375 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:36,375 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:36,376 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:36,378 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:11:36,388 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_171136_7dc3b2aa
2025-07-06 17:11:36,388 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_171136_7dc3b2aa
2025-07-06 17:11:36,389 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_171136_7dc3b2aa
2025-07-06 17:11:36,389 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.768187
2025-07-06 17:11:36,389 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:11:36,411 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-16
2025-07-06 17:11:36,411 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:11:36,412 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:11:36,412 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-23
2025-07-06 17:11:36,412 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-24
2025-07-06 17:11:36,412 - __main__ - INFO - 🆕 开始第4周 - A/B测试阶段: 2025-01-24
2025-07-06 17:11:36,412 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第1/7天
2025-07-06 17:11:36,412 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-27
2025-07-06 17:11:36,412 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第4/7天
2025-07-06 17:11:36,412 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-28
2025-07-06 17:11:36,412 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第5/7天
2025-07-06 17:11:36,412 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-29
2025-07-06 17:11:36,414 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第6/7天
2025-07-06 17:11:36,414 - __main__ - INFO - ✅ 第 3 周完成: baseline_complete
2025-07-06 17:11:36,414 - __main__ - INFO - 🔄 开始第 4 周（ab_testing）: 2025-01-30 到 2025-02-07
2025-07-06 17:11:36,414 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-30
2025-07-06 17:11:36,414 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第7/7天
2025-07-06 17:11:36,414 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:11:36,414 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:11:36,414 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:11:36,414 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:11:36,415 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:11:36,415 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:11:36,415 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:11:36,415 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:11:36,415 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_171136
2025-07-06 17:11:36,415 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_171136 - 0 条记录
2025-07-06 17:11:36,416 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_171136
2025-07-06 17:11:36,416 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:11:36,416 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-31
2025-07-06 17:11:36,416 - __main__ - INFO - 🆕 开始第5周 - 基线运行阶段: 2025-01-31
2025-07-06 17:11:36,416 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第1/7天
2025-07-06 17:11:36,416 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-03
2025-07-06 17:11:36,416 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第4/7天
2025-07-06 17:11:36,416 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-04
2025-07-06 17:11:36,416 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第5/7天
2025-07-06 17:11:36,416 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-05
2025-07-06 17:11:36,416 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第6/7天
2025-07-06 17:11:36,417 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-06
2025-07-06 17:11:36,417 - __main__ - INFO - 📊 执行基线运行阶段 - 第5周，第7/7天
2025-07-06 17:11:36,417 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:11:36,417 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:11:36,417 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:11:36,420 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:11:36,420 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:11:36,421 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:11:36,421 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:11:36,421 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:11:36,421 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:11:36,421 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:11:36,421 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.030006
2025-07-06 17:11:36,422 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:11:36,422 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.026732
2025-07-06 17:11:36,422 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:11:36,422 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.065808
2025-07-06 17:11:36,422 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:11:36,423 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.002858
2025-07-06 17:11:36,423 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:11:36,423 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.010258
2025-07-06 17:11:36,423 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:11:36,423 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.036296
2025-07-06 17:11:36,423 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:11:36,423 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.059725
2025-07-06 17:11:36,423 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.231683 = 大联盟值 0.231683
2025-07-06 17:11:36,424 - __main__ - INFO - Shapley值计算完成，耗时 0.003s
2025-07-06 17:11:36,424 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:11:36,424 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:11:36,424 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:11:36,424 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:11:36,424 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-31
2025-07-06 17:11:36,424 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:11:36,424 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:11:36,425 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:36,425 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:36,427 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:36,427 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:36,428 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:36,428 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:36,428 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:39,794 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617113706b915eaa72d4084'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:39,795 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:39,796 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:39,796 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:39,797 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:39,797 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:39,797 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:39,799 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:39,799 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:39,800 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:39,801 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:39,801 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:39,801 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:39,802 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:42,660 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171140a52aa62b7dff460a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:42,660 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:42,660 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:42,660 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:42,661 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:42,661 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:42,661 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:42,662 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:42,662 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:42,664 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:42,664 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:42,664 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:42,664 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:42,664 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:45,281 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171143ea06e0da19f7446d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:45,282 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:45,282 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:45,282 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:45,283 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:45,283 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:45,283 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:45,284 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:45,284 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:45,284 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:45,284 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:45,285 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:45,285 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:45,286 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:48,977 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:49 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061711454f2fb1024e6c4070'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:48,977 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:48,977 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:48,977 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:48,977 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:48,977 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:48,977 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:48,978 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:48,978 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:48,979 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:48,980 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:48,980 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:48,980 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:48,980 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:51,528 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171149b812e6f438874210'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:51,528 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:51,530 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:51,530 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:51,531 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:51,531 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:51,532 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:51,533 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:51,533 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:51,534 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:51,534 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:51,535 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:51,535 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:51,536 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:55,070 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:55 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171152b3eff451afa942c2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:55,072 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:55,072 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:55,073 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:55,073 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:55,073 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:55,074 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:55,076 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:55,076 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:55,078 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:55,079 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:55,079 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:55,080 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:55,080 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:57,472 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:11:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171155b30c13d73fe94585'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:57,474 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:57,474 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:57,474 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:57,475 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:57,476 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:57,476 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:57,478 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:11:57,478 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:11:57,479 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:11:57,480 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:11:57,480 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:11:57,481 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:11:57,481 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:11:59,987 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061711585e7657c40f614a57'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:11:59,989 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:11:59,990 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:11:59,990 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:11:59,991 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:11:59,991 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:11:59,991 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:11:59,993 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:12:00,002 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_171159_4e21b097
2025-07-06 17:12:00,004 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_171159_4e21b097
2025-07-06 17:12:00,004 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_171159_4e21b097
2025-07-06 17:12:00,005 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.784721
2025-07-06 17:12:00,006 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:12:00,006 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:12:00,007 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:12:00,009 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:00,010 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:00,011 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:00,012 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:00,012 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:00,013 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:00,014 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:03,235 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171200386b7715dfee4887'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:03,237 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:03,237 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:03,238 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:03,238 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:03,239 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:03,239 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:03,241 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:03,241 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:03,242 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:03,243 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:03,243 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:03,243 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:03,244 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:05,983 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171203cd6e6744b0b84052'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:05,984 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:05,984 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:05,984 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:05,984 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:05,985 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:05,985 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:05,987 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:05,987 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:05,989 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:05,989 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:05,990 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:05,990 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:05,990 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:08,776 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712065895b2f7044b469f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:08,776 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:08,776 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:08,776 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:08,777 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:08,777 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:08,777 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:08,778 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:08,778 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:08,779 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:08,779 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:08,780 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:08,780 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:08,780 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:11,955 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712091ec07e970e6d4dd9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:11,957 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:11,957 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:11,958 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:11,958 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:11,959 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:11,959 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:11,961 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:11,962 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:11,964 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:11,964 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:11,965 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:11,965 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:11,965 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:16,387 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617121216a338310adb4ca5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:16,387 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:16,388 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:16,389 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:16,389 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:16,389 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:16,390 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:16,391 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:16,391 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:16,392 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:16,393 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:16,393 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:16,393 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:16,394 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:20,431 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171217255cfb48777c45c3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:20,432 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:20,433 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:20,434 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:20,434 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:20,434 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:20,434 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:20,437 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:20,438 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:20,439 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:20,440 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:20,440 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:20,441 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:20,441 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:24,326 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:24 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617122192764fc189fd400c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:24,327 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:24,327 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:24,328 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:24,328 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:24,328 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:24,328 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:24,330 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:24,330 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:24,331 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:24,331 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:24,332 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:24,332 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:24,332 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:26,913 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712258291f0ed78464194'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:26,914 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:26,915 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:26,916 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:26,916 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:26,917 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:26,917 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:26,919 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:12:26,928 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_171226_ae19aa85
2025-07-06 17:12:26,928 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_171226_ae19aa85
2025-07-06 17:12:26,929 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_171226_ae19aa85
2025-07-06 17:12:26,929 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.817968
2025-07-06 17:12:26,930 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:12:26,960 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-31
2025-07-06 17:12:26,960 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:12:26,960 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:12:26,960 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-07
2025-07-06 17:12:26,960 - __main__ - INFO - ✅ 第 4 周完成: baseline_complete
2025-07-06 17:12:26,961 - __main__ - INFO - 🔄 开始第 5 周（baseline_operation）: 2025-02-10 到 2025-02-18
2025-07-06 17:12:26,961 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-10
2025-07-06 17:12:26,961 - __main__ - INFO - 🆕 开始第6周 - A/B测试阶段: 2025-02-10
2025-07-06 17:12:26,961 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第1/7天
2025-07-06 17:12:26,962 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-11
2025-07-06 17:12:26,962 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第2/7天
2025-07-06 17:12:26,962 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-12
2025-07-06 17:12:26,962 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第3/7天
2025-07-06 17:12:26,962 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-13
2025-07-06 17:12:26,964 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第4/7天
2025-07-06 17:12:26,964 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-14
2025-07-06 17:12:26,964 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第6周，第5/7天
2025-07-06 17:12:26,964 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-17
2025-07-06 17:12:26,965 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-18
2025-07-06 17:12:26,965 - __main__ - INFO - 🆕 开始第7周 - 基线运行阶段: 2025-02-18
2025-07-06 17:12:26,965 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第1/7天
2025-07-06 17:12:26,966 - __main__ - INFO - ✅ 第 5 周完成: in_progress
2025-07-06 17:12:26,966 - __main__ - INFO - 🔄 开始第 6 周（ab_testing）: 2025-02-19 到 2025-02-27
2025-07-06 17:12:26,966 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-19
2025-07-06 17:12:26,966 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第2/7天
2025-07-06 17:12:26,966 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-20
2025-07-06 17:12:26,967 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第3/7天
2025-07-06 17:12:26,967 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-21
2025-07-06 17:12:26,967 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第4/7天
2025-07-06 17:12:26,967 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-24
2025-07-06 17:12:26,967 - __main__ - INFO - 📊 执行基线运行阶段 - 第7周，第7/7天
2025-07-06 17:12:26,968 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:12:26,968 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:12:26,968 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:12:26,974 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:12:26,975 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:12:26,975 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:12:26,976 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:12:26,976 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:12:26,976 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:12:26,976 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:12:26,977 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.030006
2025-07-06 17:12:26,977 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:12:26,977 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.026732
2025-07-06 17:12:26,977 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:12:26,978 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.065808
2025-07-06 17:12:26,978 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:12:26,978 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.002858
2025-07-06 17:12:26,978 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:12:26,978 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.010258
2025-07-06 17:12:26,979 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:12:26,979 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.036296
2025-07-06 17:12:26,979 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:12:26,979 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.059725
2025-07-06 17:12:26,980 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.231683 = 大联盟值 0.231683
2025-07-06 17:12:26,980 - __main__ - INFO - Shapley值计算完成，耗时 0.005s
2025-07-06 17:12:26,981 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:12:26,981 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:12:26,981 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:12:26,981 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:12:26,981 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-02-18
2025-07-06 17:12:26,982 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:12:26,982 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:12:26,984 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:26,984 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:26,985 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:26,986 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:26,986 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:26,986 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:26,987 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:29,669 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617122799d1590fee674142'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:29,670 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:29,670 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:29,671 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:29,671 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:29,672 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:29,672 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:29,675 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:29,675 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:29,676 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:29,677 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:29,677 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:29,677 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:29,677 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:33,119 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:33 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171230d5b2256bfa7444f2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:33,120 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:33,120 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:33,121 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:33,121 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:33,122 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:33,122 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:33,124 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:33,125 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:33,126 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:33,127 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:33,127 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:33,127 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:33,128 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:36,930 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:37 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712330827f20b227b41ce'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:36,931 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:36,932 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:36,932 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:36,932 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:36,933 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:36,934 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:36,935 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:36,935 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:36,937 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:36,939 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:36,939 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:36,939 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:36,939 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:39,138 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712379945e3fcf9584eae'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:39,140 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:39,140 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:39,141 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:39,141 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:39,141 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:39,142 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:39,144 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:39,144 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:39,145 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:39,146 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:39,146 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:39,147 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:39,147 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:42,054 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171239e376a82418414b49'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:42,054 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:42,055 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:42,055 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:42,055 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:42,055 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:42,056 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:42,057 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:42,057 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:42,058 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:42,058 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:42,058 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:42,058 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:42,058 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:45,026 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712423e4f35be99a344b1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:45,027 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:45,027 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:45,028 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:45,029 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:45,029 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:45,029 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:45,031 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:45,032 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:45,033 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:45,033 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:45,035 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:45,035 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:45,035 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:48,289 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:48 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712450eac6a51f20048f4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:48,290 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:48,290 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:48,290 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:48,291 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:48,291 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:48,291 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:48,292 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:48,292 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:48,292 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:48,292 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:48,292 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:48,292 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:48,292 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:52,516 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171248a251255603c64318'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:52,517 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:52,518 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:52,518 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:52,519 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:52,519 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:52,519 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:52,522 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:12:52,531 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_171252_020f121e
2025-07-06 17:12:52,532 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_171252_020f121e
2025-07-06 17:12:52,532 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_171252_020f121e
2025-07-06 17:12:52,532 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.615115
2025-07-06 17:12:52,532 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:12:52,532 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:12:52,533 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:12:52,533 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:52,533 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:52,535 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:52,535 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:52,536 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:52,536 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:52,536 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:12:56,766 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:12:57 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061712539a7a3305638545c4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:12:56,766 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:12:56,767 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:12:56,767 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:12:56,769 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:12:56,769 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:12:56,769 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:12:56,771 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:12:56,771 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:12:56,773 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:12:56,774 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:12:56,774 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:12:56,775 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:12:56,775 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:02,048 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171257c85c40bc2d7e4a74'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:02,049 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:02,050 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:02,050 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:02,051 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:02,051 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:02,051 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:02,054 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:02,054 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:02,056 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:02,057 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:02,057 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:02,058 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:02,058 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:06,295 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713022a74c26c51b04615'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:06,296 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:06,296 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:06,297 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:06,297 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:06,297 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:06,297 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:06,298 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:06,298 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:06,299 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:06,299 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:06,299 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:06,299 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:06,300 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:11,311 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713068be7309adc854469'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:11,311 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:11,312 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:11,312 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:11,312 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:11,314 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:11,314 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:11,316 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:11,316 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:11,318 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:11,318 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:11,318 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:11,319 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:11,319 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:16,021 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713117bf6676c67594211'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:16,021 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:16,022 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:16,022 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:16,022 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:16,022 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:16,022 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:16,024 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:16,024 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:16,024 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:16,025 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:16,025 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:16,025 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:16,025 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:19,420 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617131681293f3e06654bbf'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:19,421 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:19,422 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:19,422 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:19,422 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:19,422 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:19,422 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:19,424 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:19,425 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:19,426 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:19,427 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:19,428 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:19,428 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:19,428 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:22,729 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713203eb0860e87c24a4e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:22,729 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:22,730 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:22,730 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:22,731 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:22,731 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:22,731 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:22,734 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:22,734 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:22,736 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:22,737 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:22,737 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:22,738 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:22,738 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:26,306 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713239a46e38023a04cd3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:26,308 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:26,308 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:26,309 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:26,309 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:26,309 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:26,310 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:26,311 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:13:26,319 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_171326_4c00b5ae
2025-07-06 17:13:26,320 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_171326_4c00b5ae
2025-07-06 17:13:26,320 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_171326_4c00b5ae
2025-07-06 17:13:26,321 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.838930
2025-07-06 17:13:26,321 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:13:26,337 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-02-18
2025-07-06 17:13:26,337 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:13:26,338 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:13:26,338 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-25
2025-07-06 17:13:26,338 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-26
2025-07-06 17:13:26,338 - __main__ - INFO - 🆕 开始第8周 - A/B测试阶段: 2025-02-26
2025-07-06 17:13:26,338 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第1/7天
2025-07-06 17:13:26,338 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-27
2025-07-06 17:13:26,338 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第2/7天
2025-07-06 17:13:26,339 - __main__ - INFO - ✅ 第 6 周完成: baseline_complete
2025-07-06 17:13:26,339 - __main__ - INFO - 🔄 开始第 7 周（baseline_operation）: 2025-02-28 到 2025-03-10
2025-07-06 17:13:26,339 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-02-28
2025-07-06 17:13:26,339 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第3/7天
2025-07-06 17:13:26,339 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-03
2025-07-06 17:13:26,339 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第6/7天
2025-07-06 17:13:26,339 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-04
2025-07-06 17:13:26,339 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第8周，第7/7天
2025-07-06 17:13:26,339 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:13:26,340 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:13:26,340 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:13:26,340 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:13:26,340 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:13:26,340 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:13:26,340 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:13:26,340 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:13:26,340 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_171326
2025-07-06 17:13:26,341 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_171326 - 0 条记录
2025-07-06 17:13:26,341 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_171326
2025-07-06 17:13:26,341 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:13:26,341 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-05
2025-07-06 17:13:26,342 - __main__ - INFO - 🆕 开始第9周 - 基线运行阶段: 2025-03-05
2025-07-06 17:13:26,342 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第1/7天
2025-07-06 17:13:26,342 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-06
2025-07-06 17:13:26,342 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第2/7天
2025-07-06 17:13:26,342 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-07
2025-07-06 17:13:26,342 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第3/7天
2025-07-06 17:13:26,342 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-10
2025-07-06 17:13:26,342 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第6/7天
2025-07-06 17:13:26,342 - __main__ - INFO - ✅ 第 7 周完成: ab_testing_complete
2025-07-06 17:13:26,342 - __main__ - INFO - 🔬 第 7 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:13:26,342 - __main__ - WARNING - ⚠️  第 7 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:13:26,342 - __main__ - INFO - 🔄 开始第 8 周（ab_testing）: 2025-03-11 到 2025-03-19
2025-07-06 17:13:26,342 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-11
2025-07-06 17:13:26,344 - __main__ - INFO - 📊 执行基线运行阶段 - 第9周，第7/7天
2025-07-06 17:13:26,344 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:13:26,344 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:13:26,344 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:13:26,347 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:13:26,347 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:13:26,347 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:13:26,348 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:13:26,348 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:13:26,348 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:13:26,348 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:13:26,348 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.030006
2025-07-06 17:13:26,348 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:13:26,348 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.026732
2025-07-06 17:13:26,348 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:13:26,348 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.065808
2025-07-06 17:13:26,349 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:13:26,349 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.002858
2025-07-06 17:13:26,349 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:13:26,349 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.010258
2025-07-06 17:13:26,349 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:13:26,349 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.036296
2025-07-06 17:13:26,349 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:13:26,349 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.059725
2025-07-06 17:13:26,349 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.231683 = 大联盟值 0.231683
2025-07-06 17:13:26,350 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 17:13:26,350 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:13:26,350 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:13:26,350 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:13:26,350 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:13:26,350 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-03-05
2025-07-06 17:13:26,350 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:13:26,351 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:13:26,351 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:26,351 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:26,352 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:26,352 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:26,353 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:26,353 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:26,353 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:29,714 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713273947ae6c5fd443bd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:29,715 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:29,716 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:29,716 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:29,716 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:29,717 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:29,717 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:29,718 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:29,718 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:29,719 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:29,720 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:29,720 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:29,720 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:29,720 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:33,012 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:33 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171330b65dc770ea184e5f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:33,013 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:33,014 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:33,015 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:33,015 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:33,016 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:33,016 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:33,018 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:33,018 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:33,020 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:33,021 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:33,021 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:33,022 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:33,022 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:37,240 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:37 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061713337126bd52abb741e1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:37,242 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:37,242 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:37,243 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:37,243 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:37,244 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:37,244 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:37,246 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:37,246 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:37,247 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:37,248 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:37,248 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:37,248 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:37,248 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:39,609 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171337039b6c6ba5b04667'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:39,609 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:39,609 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:39,609 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:39,609 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:39,609 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:39,609 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:39,610 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:39,610 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:39,611 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:39,612 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:39,612 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:39,612 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:39,612 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:41,975 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171340a6b912d9e6b34a40'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:41,976 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:41,976 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:41,977 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:41,977 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:41,977 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:41,977 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:41,978 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:41,979 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:41,980 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:41,980 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:41,980 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:41,980 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:41,981 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:44,192 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171342e5b85ba4fb4a4b29'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:44,192 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:44,194 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:44,194 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:44,194 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:44,195 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:44,195 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:44,197 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:44,197 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:44,198 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:44,199 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:44,199 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:44,200 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:44,200 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:47,501 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:48 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171344d8ae4dc13c9e4c51'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:47,501 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:47,502 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:47,503 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:47,504 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:47,504 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:47,505 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:47,507 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:47,507 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:47,509 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:47,509 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:47,510 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:47,510 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:47,510 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:51,777 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617134841db0b5a4a404787'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:51,777 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:51,779 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:51,779 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:51,780 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:51,780 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:51,780 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:51,782 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:13:51,789 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_171351_0e5f9c9b
2025-07-06 17:13:51,789 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_171351_0e5f9c9b
2025-07-06 17:13:51,790 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_171351_0e5f9c9b
2025-07-06 17:13:51,790 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.782672
2025-07-06 17:13:51,790 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:13:51,791 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:13:51,791 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:13:51,791 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:51,792 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:51,792 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:51,793 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:51,793 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:51,793 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:51,793 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:13:57,116 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:13:57 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171352f878fa504cf44121'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:13:57,117 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:13:57,117 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:13:57,118 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:13:57,119 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:13:57,119 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:13:57,119 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:13:57,121 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:13:57,122 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:13:57,124 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:13:57,125 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:13:57,125 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:13:57,126 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:13:57,126 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:00,441 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:01 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171357abe9518679b64cdd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:00,442 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:00,442 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:00,443 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:00,444 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:00,444 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:00,444 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:00,446 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:00,446 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:00,447 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:00,448 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:00,449 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:00,449 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:00,449 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:04,712 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:05 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061714018a22ee123425462e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:04,713 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:04,713 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:04,713 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:04,713 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:04,713 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:04,714 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:04,714 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:04,715 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:04,715 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:04,715 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:04,716 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:04,716 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:04,717 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:07,623 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:08 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171405d3c9fcae70954c73'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:07,623 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:07,625 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:07,625 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:07,626 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:07,626 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:07,626 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:07,627 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:07,629 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:07,630 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:07,631 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:07,632 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:07,632 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:07,632 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:12,966 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617140833c0908956a548d9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:12,966 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:12,966 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:12,967 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:12,967 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:12,967 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:12,967 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:12,969 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:12,969 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:12,970 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:12,970 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:12,971 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:12,972 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:12,972 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:16,182 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171413e631cc9ab6df4bf2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:16,183 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:16,183 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:16,184 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:16,185 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:16,185 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:16,185 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:16,188 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:16,188 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:16,190 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:16,192 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:16,192 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:16,192 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:16,192 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:20,203 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061714169be9c08c01ef4df3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:20,205 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:20,205 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:20,206 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:20,206 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:20,206 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:20,206 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:20,208 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:20,208 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:20,209 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:20,209 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:20,210 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:20,210 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:20,210 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:24,428 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061714202959a7560a724685'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:24,430 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:24,430 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:24,431 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:24,431 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:24,431 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:24,432 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:24,434 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:14:24,444 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_171424_86e3fcca
2025-07-06 17:14:24,444 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_171424_86e3fcca
2025-07-06 17:14:24,444 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_171424_86e3fcca
2025-07-06 17:14:24,444 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.780012
2025-07-06 17:14:24,445 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:14:24,458 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-03-05
2025-07-06 17:14:24,458 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:14:24,459 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:14:24,459 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-12
2025-07-06 17:14:24,459 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-13
2025-07-06 17:14:24,459 - __main__ - INFO - 🆕 开始第10周 - A/B测试阶段: 2025-03-13
2025-07-06 17:14:24,459 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第1/7天
2025-07-06 17:14:24,459 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-14
2025-07-06 17:14:24,459 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第2/7天
2025-07-06 17:14:24,460 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-17
2025-07-06 17:14:24,460 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第5/7天
2025-07-06 17:14:24,460 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-18
2025-07-06 17:14:24,460 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第6/7天
2025-07-06 17:14:24,460 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-19
2025-07-06 17:14:24,460 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第10周，第7/7天
2025-07-06 17:14:24,460 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 17:14:24,460 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:14:24,460 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:14:24,460 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 17:14:24,461 - __main__ - INFO - ✅ 智能体 BOA 配置更新成功: optimized
2025-07-06 17:14:24,461 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 17:14:24,461 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 17:14:24,461 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:14:24,461 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_171424
2025-07-06 17:14:24,461 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_171424 - 0 条记录
2025-07-06 17:14:24,461 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_171424
2025-07-06 17:14:24,461 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:14:24,461 - __main__ - INFO - ✅ 第 8 周完成: ab_testing_complete
2025-07-06 17:14:24,461 - __main__ - INFO - 🔬 第 8 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 17:14:24,461 - __main__ - WARNING - ⚠️  第 8 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 17:14:24,461 - __main__ - INFO - 🔄 开始第 9 周（baseline_operation）: 2025-03-20 到 2025-03-28
2025-07-06 17:14:24,463 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-20
2025-07-06 17:14:24,463 - __main__ - INFO - 🆕 开始第11周 - 基线运行阶段: 2025-03-20
2025-07-06 17:14:24,463 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第1/7天
2025-07-06 17:14:24,463 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-21
2025-07-06 17:14:24,463 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第2/7天
2025-07-06 17:14:24,463 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-24
2025-07-06 17:14:24,463 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第5/7天
2025-07-06 17:14:24,463 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-25
2025-07-06 17:14:24,463 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第6/7天
2025-07-06 17:14:24,463 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-26
2025-07-06 17:14:24,463 - __main__ - INFO - 📊 执行基线运行阶段 - 第11周，第7/7天
2025-07-06 17:14:24,464 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 17:14:24,464 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:14:24,464 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:14:24,467 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:14:24,468 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:14:24,468 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:14:24,468 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 17:14:24,468 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:14:24,468 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 17:14:24,468 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 17:14:24,468 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.030006
2025-07-06 17:14:24,468 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 17:14:24,469 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.026732
2025-07-06 17:14:24,469 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 17:14:24,469 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.065808
2025-07-06 17:14:24,469 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 17:14:24,469 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.002858
2025-07-06 17:14:24,469 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 17:14:24,469 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.010258
2025-07-06 17:14:24,469 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 17:14:24,469 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.036296
2025-07-06 17:14:24,470 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 17:14:24,470 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.059725
2025-07-06 17:14:24,470 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.231683 = 大联盟值 0.231683
2025-07-06 17:14:24,470 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 17:14:24,470 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:14:24,471 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:14:24,471 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BOA', 'BeOA']
2025-07-06 17:14:24,471 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BOA', 'BeOA']
2025-07-06 17:14:24,471 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-03-20
2025-07-06 17:14:24,472 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 17:14:24,472 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:14:24,473 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:24,473 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:24,474 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:24,474 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:24,475 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:24,475 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:24,475 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:28,807 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171425f852245aef5b4b29'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:28,808 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:28,808 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:28,809 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:28,809 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:28,809 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:28,811 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:28,813 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:28,813 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:28,814 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:28,815 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:28,815 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:28,816 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:28,816 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:31,811 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061714290f32dd86e0da49d9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:31,812 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:31,812 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:31,812 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:31,813 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:31,813 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:31,814 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:31,815 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:31,816 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:31,817 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:31,818 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:31,819 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:31,819 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:31,819 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:35,582 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171432ec8f4c2e9517461a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:35,583 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:35,583 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:35,584 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:35,584 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:35,584 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:35,584 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:35,586 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:35,586 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:35,587 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:35,588 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:35,589 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:35,589 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:35,589 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:39,224 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171436ab788362389c4797'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:39,225 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:39,225 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:39,226 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:39,226 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:39,226 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:39,226 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:39,227 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:39,227 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:39,227 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:39,228 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:39,228 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:39,229 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:39,229 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:41,574 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061714395a67473b916242a2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:41,575 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:41,575 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:41,576 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:41,576 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:41,577 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:41,577 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:41,579 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:41,579 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:41,581 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:41,582 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:41,582 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:41,583 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:41,583 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:44,681 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171442ec4f1d56edb24f7f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:44,682 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:44,682 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:44,682 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:44,682 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:44,682 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:44,684 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:44,685 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:44,685 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:44,686 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:44,686 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:44,686 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:44,687 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:44,687 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:47,078 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617144557c3eb7518df4a9c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:47,079 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:47,079 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:47,080 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:47,080 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:47,080 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:47,080 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:47,081 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:47,081 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:47,081 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:47,082 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:47,082 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:47,082 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:47,082 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:49,937 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:50 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061714472858234001094165'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:49,938 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:49,939 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:49,939 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:49,939 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:49,940 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:49,940 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:49,941 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:14:49,947 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_171449_acc14df1
2025-07-06 17:14:49,947 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_171449_acc14df1
2025-07-06 17:14:49,947 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_171449_acc14df1
2025-07-06 17:14:49,947 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.759043
2025-07-06 17:14:49,948 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 17:14:49,948 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 17:14:49,948 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 17:14:49,949 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:49,949 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:49,950 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:49,950 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:49,950 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:49,950 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:49,950 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:53,216 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171450b99029622fa04ad8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:53,216 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:53,217 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:53,218 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:53,218 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:53,219 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:53,219 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:53,222 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:53,222 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:53,225 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:53,225 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:53,226 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:53,226 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:53,226 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:14:57,533 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:14:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171453de8ddba664d342a2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:14:57,534 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:14:57,535 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:14:57,535 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:14:57,536 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:14:57,536 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:14:57,537 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:14:57,539 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:14:57,539 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:14:57,540 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:14:57,541 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:14:57,541 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:14:57,542 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:14:57,542 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:15:05,476 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:15:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617145853ccdcd2df654679'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:15:05,477 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:15:05,478 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:15:05,479 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:15:05,479 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:15:05,479 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:15:05,480 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:15:05,482 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:15:05,482 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:15:05,483 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:15:05,485 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:15:05,485 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:15:05,486 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:15:05,486 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:15:08,621 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:15:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171506c8ad8ffa4d0b451a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:15:08,622 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:15:08,622 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:15:08,624 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:15:08,624 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:15:08,624 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:15:08,625 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:15:08,627 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:15:08,628 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:15:08,629 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:15:08,629 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:15:08,630 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:15:08,630 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:15:08,630 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:15:15,347 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:15:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171509675bb638a14b44d2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:15:15,349 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:15:15,349 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:15:15,350 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:15:15,350 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:15:15,351 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:15:15,351 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:15:15,353 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:15:15,353 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:15:15,354 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:15:15,355 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:15:15,355 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:15:15,356 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:15:15,356 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:15:18,739 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:15:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171516ce8bddf2ad814a7f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:15:18,740 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:15:18,741 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:15:18,742 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:15:18,742 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:15:18,742 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:15:18,742 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:15:18,744 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:15:18,744 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:15:18,745 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:15:18,746 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:15:18,747 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:15:18,748 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:15:18,748 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:15:21,607 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:15:22 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070617151906f17a1fc7794295'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:15:21,607 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:15:21,608 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:15:21,609 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:15:21,609 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:15:21,610 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:15:21,610 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:15:21,612 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 17:15:21,612 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 17:15:21,614 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 17:15:21,614 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 17:15:21,615 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 17:15:21,615 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 17:15:21,615 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 17:15:24,307 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 09:15:24 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706171522d97aa6336f334df4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 17:15:24,308 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 17:15:24,308 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 17:15:24,309 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 17:15:24,309 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 17:15:24,310 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 17:15:24,310 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 17:15:24,312 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 17:15:24,323 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_171524_6c6e2541
2025-07-06 17:15:24,324 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_171524_6c6e2541
2025-07-06 17:15:24,324 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_171524_6c6e2541
2025-07-06 17:15:24,324 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.716009
2025-07-06 17:15:24,325 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 17:15:24,337 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-03-20
2025-07-06 17:15:24,337 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 17:15:24,338 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 17:15:24,338 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-27
2025-07-06 17:15:24,338 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-03-28
2025-07-06 17:15:24,338 - __main__ - INFO - 🆕 开始第12周 - A/B测试阶段: 2025-03-28
2025-07-06 17:15:24,338 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第12周，第1/7天
2025-07-06 17:15:24,338 - __main__ - INFO - ✅ 第 9 周完成: baseline_complete
2025-07-06 17:15:24,338 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 17:15:24,339 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 17:15:24,339 - __main__ - INFO -   - 总周期数: 9
2025-07-06 17:15:24,339 - __main__ - INFO -   - 成功周期: 8
2025-07-06 17:15:24,339 - __main__ - INFO -   - 失败周期: 1
2025-07-06 17:15:24,339 - __main__ - INFO -   - 成功率: 88.89%
2025-07-06 17:15:24,339 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 17:15:24,339 - __main__ - INFO - ====================================================================================================
2025-07-06 17:15:24,339 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 17:15:24,339 - __main__ - INFO - 📊 总周期数: 9
2025-07-06 17:15:24,340 - __main__ - INFO - 📊 总交易天数: 64
2025-07-06 17:15:24,340 - __main__ - INFO - ⏱️  总执行时间: 342.94秒
2025-07-06 17:15:24,340 - __main__ - INFO - ====================================================================================================
2025-07-06 17:15:24,340 - __main__ - INFO - 开始交易会话: assessment_20250706_171524
2025-07-06 17:15:24,340 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 17:15:24,343 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_171524
2025-07-06 17:15:24,343 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_171524 (系统盈亏: 0.00)
2025-07-06 17:15:24,343 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_171524
2025-07-06 17:15:24,343 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 17:15:24,343 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 17:15:24,344 - __main__ - INFO - ====================================================================================================
2025-07-06 17:15:24,344 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 17:15:24,344 - __main__ - INFO - ====================================================================================================
