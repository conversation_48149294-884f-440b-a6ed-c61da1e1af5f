[{"timestamp": "2025-07-06T15:14:25.564345", "output_id": "output_20250706_151425_bb75df63", "input_id": "input_20250706_151418_9cd591ee", "prompt_id": "prompt_20250706_151418_535f2d74", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:14:25.564345", "processing_time": 7.04187, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:14:25.564345", "processing_time": 7.04187, "llm_used": true}, "processing_time": 7.04187, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 7.04187}}, {"timestamp": "2025-07-06T15:15:21.225856", "output_id": "output_20250706_151521_e6a104db", "input_id": "input_20250706_151514_7aba4211", "prompt_id": "prompt_20250706_151514_20931835", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "No clear trend"}, "Moving_Averages": {"50_day_MA": 165.0, "200_day_MA": 180.0, "interpretation": "Stock is currently between its 50-day and 200-day moving averages, suggesting a neutral trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:21.225856", "processing_time": 6.869497, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "No clear trend"}, "Moving_Averages": {"50_day_MA": 165.0, "200_day_MA": 180.0, "interpretation": "Stock is currently between its 50-day and 200-day moving averages, suggesting a neutral trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:21.225856", "processing_time": 6.869497, "llm_used": true}, "processing_time": 6.869497, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 13.911367}}, {"timestamp": "2025-07-06T15:15:21.778033", "output_id": "output_20250706_151521_dcaa6c0e", "input_id": "input_20250706_151515_7562bc1e", "prompt_id": "prompt_20250706_151515_54ab95ea", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "comment": "RSI is slightly above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0.05, "comment": "The MACD line is very close to the signal line, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "comment": "The stock is currently between its 50-day and 200-day moving averages, which are both rising, indicating a slightly bullish short-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:21.778033", "processing_time": 6.895723, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "comment": "RSI is slightly above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0.05, "comment": "The MACD line is very close to the signal line, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "comment": "The stock is currently between its 50-day and 200-day moving averages, which are both rising, indicating a slightly bullish short-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:21.778033", "processing_time": 6.895723, "llm_used": true}, "processing_time": 6.895723, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 20.807090000000002}}, {"timestamp": "2025-07-06T15:15:23.140472", "output_id": "output_20250706_151523_da6600f3", "input_id": "input_20250706_151514_62f1dfb8", "prompt_id": "prompt_20250706_151515_7b5035bd", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:23.140472", "processing_time": 8.520569, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:23.140472", "processing_time": 8.520569, "llm_used": true}, "processing_time": 8.520569, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 29.327659000000004}}, {"timestamp": "2025-07-06T15:15:24.426749", "output_id": "output_20250706_151524_91fe1f15", "input_id": "input_20250706_151515_211e6ac2", "prompt_id": "prompt_20250706_151515_66be177f", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 52.3, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "historical_line": 0.0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "crossover": "none", "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:24.426749", "processing_time": 9.623018, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 52.3, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "historical_line": 0.0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "crossover": "none", "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:24.426749", "processing_time": 9.623018, "llm_used": true}, "processing_time": 9.623018, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 38.950677000000006}}, {"timestamp": "2025-07-06T15:15:24.677376", "output_id": "output_20250706_151524_fefc9e89", "input_id": "input_20250706_151520_7a1928b1", "prompt_id": "prompt_20250706_151520_e3ef6f60", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:24.677376", "processing_time": 4.19673, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:24.677376", "processing_time": 4.19673, "llm_used": true}, "processing_time": 4.19673, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 43.14740700000001}}, {"timestamp": "2025-07-06T15:15:26.145595", "output_id": "output_20250706_151526_1cca71e0", "input_id": "input_20250706_151515_2e133878", "prompt_id": "prompt_20250706_151515_9ef3c828", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 50.2, "analysis": "Neutral RSI value indicates no overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "MACD signal line is close to zero, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:26.145595", "processing_time": 11.306618, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 50.2, "analysis": "Neutral RSI value indicates no overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "MACD signal line is close to zero, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:26.145595", "processing_time": 11.306618, "llm_used": true}, "processing_time": 11.306618, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 54.45402500000001}}, {"timestamp": "2025-07-06T15:15:26.483684", "output_id": "output_20250706_151526_558fe76a", "input_id": "input_20250706_151515_a8e52cd2", "prompt_id": "prompt_20250706_151515_0bd17261", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"value": 51, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "crossover": "none", "signal": "neutral"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:26.483684", "processing_time": 11.601374, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"value": 51, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "crossover": "none", "signal": "neutral"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:26.483684", "processing_time": 11.601374, "llm_used": true}, "processing_time": 11.601374, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 66.05539900000001}}, {"timestamp": "2025-07-06T15:15:26.927713", "output_id": "output_20250706_151526_00baab47", "input_id": "input_20250706_151520_78a4165d", "prompt_id": "prompt_20250706_151520_7b395198", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:26.900145", "processing_time": 6.020438, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:26.900145", "processing_time": 6.020438, "llm_used": true}, "processing_time": 6.020438, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 72.075837}}, {"timestamp": "2025-07-06T15:15:27.175396", "output_id": "output_20250706_151527_01e9c7de", "input_id": "input_20250706_151521_085f53ba", "prompt_id": "prompt_20250706_151521_e0af1589", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "overbought": true}, "MACD": {"signal_line_above_zero": true, "cross_above_signal": true}, "moving_averages": {"50_day_ma": 135.0, "200_day_ma": 120.0, "crossover": {"50_day_ma_above_200_day_ma": true}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:27.175396", "processing_time": 5.914283, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "overbought": true}, "MACD": {"signal_line_above_zero": true, "cross_above_signal": true}, "moving_averages": {"50_day_ma": 135.0, "200_day_ma": 120.0, "crossover": {"50_day_ma_above_200_day_ma": true}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:27.175396", "processing_time": 5.914283, "llm_used": true}, "processing_time": 5.914283, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 77.99012}}, {"timestamp": "2025-07-06T15:15:28.569327", "output_id": "output_20250706_151528_2c3981f6", "input_id": "input_20250706_151515_d1457495", "prompt_id": "prompt_20250706_151515_d9c6e3a1", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a sideways trend."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "Neutral - The MACD is just below the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "Neutral - The stock is currently between its 50-day and 200-day moving averages, which suggests a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:28.568333", "processing_time": 13.729356, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a sideways trend."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "Neutral - The MACD is just below the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "Neutral - The stock is currently between its 50-day and 200-day moving averages, which suggests a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:28.568333", "processing_time": 13.729356, "llm_used": true}, "processing_time": 13.729356, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 91.719476}}, {"timestamp": "2025-07-06T15:15:29.129294", "output_id": "output_20250706_151529_980b8989", "input_id": "input_20250706_151515_9514f5f5", "prompt_id": "prompt_20250706_151515_523bbdb2", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:29.129294", "processing_time": 14.408407, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:29.129294", "processing_time": 14.408407, "llm_used": true}, "processing_time": 14.408407, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 106.127883}}, {"timestamp": "2025-07-06T15:15:30.577786", "output_id": "output_20250706_151530_81af08b2", "input_id": "input_20250706_151514_f9c11cb3", "prompt_id": "prompt_20250706_151514_b5b2b04c", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"value": "positive divergence", "analysis": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 125.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:30.577786", "processing_time": 15.991141, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"value": "positive divergence", "analysis": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 125.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:30.577786", "processing_time": 15.991141, "llm_used": true}, "processing_time": 15.991141, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 122.119024}}, {"timestamp": "2025-07-06T15:15:30.794329", "output_id": "output_20250706_151530_8711590d", "input_id": "input_20250706_151523_9e5ade2c", "prompt_id": "prompt_20250706_151523_070b04b9", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:30.794329", "processing_time": 7.620878, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:30.794329", "processing_time": 7.620878, "llm_used": true}, "processing_time": 7.620878, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 129.739902}}, {"timestamp": "2025-07-06T15:15:32.734282", "output_id": "output_20250706_151532_8fff946e", "input_id": "input_20250706_151522_cfb37385", "prompt_id": "prompt_20250706_151522_eef2d904", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:32.734282", "processing_time": 10.21745, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:32.734282", "processing_time": 10.21745, "llm_used": true}, "processing_time": 10.21745, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 139.95735200000001}}, {"timestamp": "2025-07-06T15:15:33.476429", "output_id": "output_20250706_151533_ca69913c", "input_id": "input_20250706_151526_523bc0f1", "prompt_id": "prompt_20250706_151526_98f9a84a", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 61, "analysis": "The RSI is above 50, indicating that the stock is neither overbought nor oversold, which suggests a neutral trend."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "The MACD signal line is slightly above the zero line, and the histogram is negative, suggesting a slight bullish trend but with weak momentum."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 150.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which indicates a bearish trend in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:33.476429", "processing_time": 6.580567, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 61, "analysis": "The RSI is above 50, indicating that the stock is neither overbought nor oversold, which suggests a neutral trend."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "The MACD signal line is slightly above the zero line, and the histogram is negative, suggesting a slight bullish trend but with weak momentum."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 150.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which indicates a bearish trend in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:33.476429", "processing_time": 6.580567, "llm_used": true}, "processing_time": 6.580567, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 146.53791900000002}}, {"timestamp": "2025-07-06T15:15:35.199571", "output_id": "output_20250706_151535_8c090bc2", "input_id": "input_20250706_151514_3ab76752", "prompt_id": "prompt_20250706_151515_c82b5bff", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 120.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "interpretation": "Overbought, indicating potential for a pullback"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover on MACD line suggests bullish trend continuation"}, "Moving_Average": {"50_day_MA": 130.0, "200_day_MA": 140.0, "interpretation": "Stock trading above both 50-day and 200-day moving averages, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:35.199571", "processing_time": 20.523675, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 120.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "interpretation": "Overbought, indicating potential for a pullback"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover on MACD line suggests bullish trend continuation"}, "Moving_Average": {"50_day_MA": 130.0, "200_day_MA": 140.0, "interpretation": "Stock trading above both 50-day and 200-day moving averages, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:35.199571", "processing_time": 20.523675, "llm_used": true}, "processing_time": 20.523675, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 167.061594}}, {"timestamp": "2025-07-06T15:15:40.495412", "output_id": "output_20250706_151540_77731b8c", "input_id": "input_20250706_151531_78b9bd7a", "prompt_id": "prompt_20250706_151531_90df8fe5", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": {"bullish": true, "cross": false}}, "moving_averages": {"50_day": 135.0, "200_day": 125.0, "cross": {"50_day_above_200_day": true}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:40.477352", "processing_time": 8.674986, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": {"bullish": true, "cross": false}}, "moving_averages": {"50_day": 135.0, "200_day": 125.0, "cross": {"50_day_above_200_day": true}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:40.477352", "processing_time": 8.674986, "llm_used": true}, "processing_time": 8.674986, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 175.73658}}, {"timestamp": "2025-07-06T15:15:42.377496", "output_id": "output_20250706_151542_43e2cefe", "input_id": "input_20250706_151537_9cd819a3", "prompt_id": "prompt_20250706_151537_d3976ad4", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD signal line is slightly above the zero line, suggesting a weak bullish trend."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 150.0, "analysis": "The stock is currently below both the 50-day and 200-day moving averages, which are resistance levels."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:42.377496", "processing_time": 4.628353, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD signal line is slightly above the zero line, suggesting a weak bullish trend."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 150.0, "analysis": "The stock is currently below both the 50-day and 200-day moving averages, which are resistance levels."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:42.377496", "processing_time": 4.628353, "llm_used": true}, "processing_time": 4.628353, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 180.364933}}, {"timestamp": "2025-07-06T15:15:43.601268", "output_id": "output_20250706_151543_36f7a5cd", "input_id": "input_20250706_151536_f66d7ac8", "prompt_id": "prompt_20250706_151536_34679516", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "RSI is close to the middle line, indicating a neutral trend."}, "MACD": {"signal_line": 0.01, "histogram": 0.0, "analysis": "The MACD line is just above the signal line with a very low histogram, suggesting a very slight bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock price is currently below both the 50-day and 200-day moving averages, indicating a possible bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:43.596271", "processing_time": 6.785327, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "RSI is close to the middle line, indicating a neutral trend."}, "MACD": {"signal_line": 0.01, "histogram": 0.0, "analysis": "The MACD line is just above the signal line with a very low histogram, suggesting a very slight bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock price is currently below both the 50-day and 200-day moving averages, indicating a possible bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:43.596271", "processing_time": 6.785327, "llm_used": true}, "processing_time": 6.785327, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 187.15026}}, {"timestamp": "2025-07-06T15:15:44.880782", "output_id": "output_20250706_151544_cbfbe8bd", "input_id": "input_20250706_151540_e0295278", "prompt_id": "prompt_20250706_151540_b5b154bc", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:44.872777", "processing_time": 4.113106, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:44.872777", "processing_time": 4.113106, "llm_used": true}, "processing_time": 4.113106, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 191.263366}}, {"timestamp": "2025-07-06T15:15:48.230460", "output_id": "output_20250706_151548_d451039b", "input_id": "input_20250706_151540_92a0f11e", "prompt_id": "prompt_20250706_151540_6268c34c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "reading": "overbought", "signal": "sell"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "reading": "positive", "signal": "buy"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "reading": "bullish", "signal": "buy"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:48.230460", "processing_time": 7.793151, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "reading": "overbought", "signal": "sell"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "reading": "positive", "signal": "buy"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "reading": "bullish", "signal": "buy"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:48.230460", "processing_time": 7.793151, "llm_used": true}, "processing_time": 7.793151, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 199.05651699999999}}, {"timestamp": "2025-07-06T15:15:48.789136", "output_id": "output_20250706_151548_e7c01aa6", "input_id": "input_20250706_151541_19a4dd4f", "prompt_id": "prompt_20250706_151541_e5f6148d", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:48.789136", "processing_time": 7.116433, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:48.789136", "processing_time": 7.116433, "llm_used": true}, "processing_time": 7.116433, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 206.17295}}, {"timestamp": "2025-07-06T15:15:50.407358", "output_id": "output_20250706_151550_eaefcc1c", "input_id": "input_20250706_151543_6457cf7f", "prompt_id": "prompt_20250706_151543_76ceb695", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "interpretation": "MACD signal line is close to zero and histogram is close to zero, suggesting a neutral trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA is above 200-day MA, indicating a slight bullish trend, but the 50-day MA is close to the current price, suggesting potential reversal"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:50.407358", "processing_time": 6.662437, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "interpretation": "MACD signal line is close to zero and histogram is close to zero, suggesting a neutral trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA is above 200-day MA, indicating a slight bullish trend, but the 50-day MA is close to the current price, suggesting potential reversal"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:50.407358", "processing_time": 6.662437, "llm_used": true}, "processing_time": 6.662437, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 212.835387}}, {"timestamp": "2025-07-06T15:15:53.624180", "output_id": "output_20250706_151553_6e392bd0", "input_id": "input_20250706_151548_979c1b9b", "prompt_id": "prompt_20250706_151549_e38ba762", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "price above both moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:53.624180", "processing_time": 4.6432, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "price above both moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:53.624180", "processing_time": 4.6432, "llm_used": true}, "processing_time": 4.6432, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 217.478587}}, {"timestamp": "2025-07-06T15:15:55.562855", "output_id": "output_20250706_151555_da6e804a", "input_id": "input_20250706_151551_dce5c5fe", "prompt_id": "prompt_20250706_151551_063f22cd", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:55.562855", "processing_time": 3.963328, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:55.562855", "processing_time": 3.963328, "llm_used": true}, "processing_time": 3.963328, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 221.441915}}, {"timestamp": "2025-07-06T15:15:56.896242", "output_id": "output_20250706_151556_4c7ec7ed", "input_id": "input_20250706_151551_b7fce60a", "prompt_id": "prompt_20250706_151551_2d70598b", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 62, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": -0.01, "interpretation": "weak bullish trend"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 160.0, "interpretation": "price above 50-day MA, but below 200-day MA, indicating a moderate bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:56.896242", "processing_time": 5.650464, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 62, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": -0.01, "interpretation": "weak bullish trend"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 160.0, "interpretation": "price above 50-day MA, but below 200-day MA, indicating a moderate bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:56.896242", "processing_time": 5.650464, "llm_used": true}, "processing_time": 5.650464, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 227.092379}}, {"timestamp": "2025-07-06T15:15:57.827127", "output_id": "output_20250706_151557_b049f3b2", "input_id": "input_20250706_151552_51934403", "prompt_id": "prompt_20250706_151552_cadad19f", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "comment": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "comment": "MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a short-term bullish trend with a longer-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:57.827127", "processing_time": 5.65564, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "comment": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "comment": "MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a short-term bullish trend with a longer-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:57.827127", "processing_time": 5.65564, "llm_used": true}, "processing_time": 5.65564, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 232.748019}}, {"timestamp": "2025-07-06T15:15:59.119999", "output_id": "output_20250706_151559_7fe9ba3d", "input_id": "input_20250706_151551_6b35d62b", "prompt_id": "prompt_20250706_151551_49fd2aa0", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 70, indicating the stock may be overbought, but the strong bullish sentiment from news and momentum suggests this is temporary."}, "MACD": {"current_value": "positive", "comment": "The MACD is showing a positive crossover, which is a bullish signal."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:59.119999", "processing_time": 7.305073, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 70, indicating the stock may be overbought, but the strong bullish sentiment from news and momentum suggests this is temporary."}, "MACD": {"current_value": "positive", "comment": "The MACD is showing a positive crossover, which is a bullish signal."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:15:59.119999", "processing_time": 7.305073, "llm_used": true}, "processing_time": 7.305073, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 240.053092}}, {"timestamp": "2025-07-06T15:16:14.191107", "output_id": "output_20250706_151614_7289cc51", "input_id": "input_20250706_151609_dbafa2fd", "prompt_id": "prompt_20250706_151609_a4f24a65", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68.5, "comment": "RSI is above 70, indicating that the stock may be overbought, but the overall trend remains bullish."}, "MACD": {"current_value": "positive", "comment": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-day MA": "above the price", "200-day MA": "above the price", "comment": "The 50-day and 200-day moving averages are both above the current price, supporting the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:14.191107", "processing_time": 4.8315, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68.5, "comment": "RSI is above 70, indicating that the stock may be overbought, but the overall trend remains bullish."}, "MACD": {"current_value": "positive", "comment": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-day MA": "above the price", "200-day MA": "above the price", "comment": "The 50-day and 200-day moving averages are both above the current price, supporting the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:14.191107", "processing_time": 4.8315, "llm_used": true}, "processing_time": 4.8315, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 244.884592}}, {"timestamp": "2025-07-06T15:16:14.343665", "output_id": "output_20250706_151614_832f2f18", "input_id": "input_20250706_151608_6884bb06", "prompt_id": "prompt_20250706_151608_d5cdda9e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "bullish crossover"}, "MA": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "price above long-term MA, indicating bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:14.329718", "processing_time": 5.413901, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "bullish crossover"}, "MA": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "price above long-term MA, indicating bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:14.329718", "processing_time": 5.413901, "llm_used": true}, "processing_time": 5.413901, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 250.298493}}, {"timestamp": "2025-07-06T15:16:14.696598", "output_id": "output_20250706_151614_d38ae4b0", "input_id": "input_20250706_151610_ba1b1797", "prompt_id": "prompt_20250706_151610_21db4d4c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "hypothesis": "trend continuation"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:14.682356", "processing_time": 4.330132, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "hypothesis": "trend continuation"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:14.682356", "processing_time": 4.330132, "llm_used": true}, "processing_time": 4.330132, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 254.628625}}, {"timestamp": "2025-07-06T15:16:18.898582", "output_id": "output_20250706_151618_6bbce406", "input_id": "input_20250706_151604_e2c1ec2e", "prompt_id": "prompt_20250706_151604_5a0d6c83", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 72, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"short_term": {"value": 150, "signal": "above long-term"}, "long_term": {"value": 140, "signal": "bullish"}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:18.898582", "processing_time": 14.875479, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 72, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"short_term": {"value": 150, "signal": "above long-term"}, "long_term": {"value": 140, "signal": "bullish"}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:16:18.898582", "processing_time": 14.875479, "llm_used": true}, "processing_time": 14.875479, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 269.504104}}]