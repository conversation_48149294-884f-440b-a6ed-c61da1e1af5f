#!/usr/bin/env python3
"""
测试OPRO系统修复
验证'bool' object is not subscriptable错误是否已修复
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'test_opro_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def test_enhanced_weekly_opro_manager():
    """测试增强的周期性OPRO管理器"""
    logger = setup_logging()
    logger.info("开始测试增强的周期性OPRO管理器")

    try:
        # 创建配置（使用字典格式）
        config = {
            "enhanced_shapley_oprp": {
                "enabled": True,
                "cycle_length_days": 7,
                "baseline_operation_days": 7,
                "ab_testing_days": 7
            }
        }

        # 创建管理器
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        logger.info("✅ 管理器创建成功")
        
        # 测试数据收集方法
        logger.info("测试A/B测试数据收集...")
        ab_test_data = manager._collect_ab_test_data(week_number=1)
        logger.info(f"A/B测试数据: {ab_test_data}")
        
        # 测试性能比较方法
        logger.info("测试A/B测试性能比较...")
        performance_comparison = manager._compare_ab_test_performance(ab_test_data)
        logger.info(f"性能比较结果: {performance_comparison}")
        
        # 测试选择获胜提示词方法（这是出错的地方）
        logger.info("测试选择获胜提示词...")
        prompt_selection_result = manager._select_winning_prompts(performance_comparison)
        logger.info(f"提示词选择结果: {prompt_selection_result}")
        
        # 如果到这里没有出错，说明修复成功
        logger.info("✅ 所有测试通过，修复成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_edge_cases():
    """测试边界情况"""
    logger = logging.getLogger(__name__)
    logger.info("测试边界情况...")

    try:
        config = {"enhanced_shapley_oprp": {}}
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        
        # 测试空数据
        logger.info("测试空性能比较数据...")
        empty_result = manager._select_winning_prompts({})
        logger.info(f"空数据结果: {empty_result}")
        
        # 测试错误类型数据
        logger.info("测试错误类型数据...")
        wrong_type_result = manager._select_winning_prompts(True)  # 传入布尔值而不是字典
        logger.info(f"错误类型数据结果: {wrong_type_result}")
        
        # 测试不完整数据结构
        logger.info("测试不完整数据结构...")
        incomplete_data = {
            "test_experiment": {
                "config": {"optimized_agents": ["agent1", "agent2"]},
                # 缺少statistical_comparison字段
            }
        }
        incomplete_result = manager._select_winning_prompts(incomplete_data)
        logger.info(f"不完整数据结果: {incomplete_result}")
        
        logger.info("✅ 边界情况测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 边界情况测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 60)
    logger.info("开始OPRO系统修复验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1：基本功能测试
    logger.info("\n" + "=" * 40)
    logger.info("测试1：基本功能测试")
    logger.info("=" * 40)
    if test_enhanced_weekly_opro_manager():
        success_count += 1
    
    # 测试2：边界情况测试
    logger.info("\n" + "=" * 40)
    logger.info("测试2：边界情况测试")
    logger.info("=" * 40)
    if test_edge_cases():
        success_count += 1
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！OPRO系统修复成功！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
