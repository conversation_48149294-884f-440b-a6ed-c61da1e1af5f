#!/usr/bin/env python3
"""
测试OPRO系统获胜数据集成
模拟完整的OPRO系统运行，验证获胜实验数据的正确传递和使用
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'test_opro_integration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def simulate_ab_testing_week_completion():
    """模拟A/B测试周完成的完整流程"""
    logger = setup_logging()
    logger.info("模拟A/B测试周完成的完整流程")
    
    try:
        # 创建配置
        config = {
            "enhanced_shapley_oprp": {
                "enabled": True,
                "cycle_length_days": 7,
                "baseline_operation_days": 7,
                "ab_testing_days": 7,
                "underperforming_threshold": 0.3,
                "continuous_optimization": True
            }
        }
        
        # 创建管理器
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        manager.current_target_agents = ["BeOA", "TAA"]  # 设置目标智能体
        logger.info("✅ 管理器创建成功")
        
        # 模拟A/B测试数据收集
        logger.info("🔄 模拟A/B测试数据收集...")
        ab_test_data = manager._collect_ab_test_data(week_number=2)
        logger.info(f"A/B测试数据: {ab_test_data}")
        
        # 模拟性能比较
        logger.info("📊 模拟性能比较...")
        performance_comparison = manager._compare_ab_test_performance(ab_test_data)
        logger.info(f"性能比较结果: {performance_comparison}")
        
        # 模拟提示词选择
        logger.info("🏆 模拟提示词选择...")
        prompt_selection_result = manager._select_winning_prompts(performance_comparison)
        logger.info(f"提示词选择结果: {prompt_selection_result}")
        
        # 注册获胜结果到迭代Shapley计算器
        logger.info("📝 注册获胜结果到迭代Shapley计算器...")
        manager._register_winning_results_to_iterative_calculator(
            ab_test_data, performance_comparison, prompt_selection_result
        )
        
        # 生成周期总结
        logger.info("📋 生成周期总结...")
        weekly_summary = manager._finalize_weekly_cycle(
            ab_test_data, performance_comparison, prompt_selection_result
        )
        
        # 验证周期总结
        logger.info("🔍 验证周期总结...")
        if "winning_experiment_data" not in weekly_summary:
            logger.error("❌ 周期总结缺少获胜实验数据")
            return False
            
        winning_data = weekly_summary["winning_experiment_data"]
        logger.info(f"获胜实验数据: {winning_data}")
        
        # 模拟run_opro_system.py中的逻辑
        logger.info("🔬 模拟主系统中的迭代Shapley计算触发...")
        
        # 检查是否有获胜实验数据
        if winning_data:
            logger.info("✅ 发现获胜实验数据，运行迭代Shapley计算...")
            
            # 运行迭代Shapley计算
            target_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
            shapley_result = manager.iterative_shapley_calculator.calculate_iterative_shapley_values(
                target_agents=target_agents,
                use_cached_data=True
            )
            
            if shapley_result.get("success"):
                logger.info("✅ 迭代Shapley计算成功完成")
                logger.info(f"Shapley值: {shapley_result.get('shapley_values', {})}")
                return True
            else:
                logger.error(f"❌ 迭代Shapley计算失败: {shapley_result.get('error')}")
                return False
        else:
            logger.warning("⚠️ 没有获胜实验数据，跳过迭代Shapley计算")
            return False
            
    except Exception as e:
        logger.error(f"❌ 模拟测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_weekly_summary_structure():
    """测试周期总结结构的完整性"""
    logger = logging.getLogger(__name__)
    logger.info("测试周期总结结构的完整性")
    
    try:
        config = {"enhanced_shapley_oprp": {}}
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        
        # 创建测试数据
        ab_test_data = {
            "week_number": 2,
            "test_agents": ["BeOA", "TAA"],
            "data_collected": True
        }
        
        performance_comparison = {
            "ab_test_week_2": {
                "config": {"optimized_agents": ["BeOA", "TAA"]},
                "statistical_comparison": {
                    "recommendation": "optimized",
                    "comparison_complete": True,
                    "winning_track": "optimized"
                }
            }
        }
        
        prompt_selection_result = {
            "ab_test_week_2": {
                "agent_selections": {
                    "BeOA": {"selected_prompt": "optimized"},
                    "TAA": {"selected_prompt": "optimized"}
                }
            }
        }
        
        # 生成周期总结
        weekly_summary = manager._finalize_weekly_cycle(
            ab_test_data, performance_comparison, prompt_selection_result
        )
        
        # 验证结构
        expected_fields = [
            "week_number", "cycle_type", "target_agents", 
            "performance_improvement", "winning_prompts", 
            "winning_experiment_data", "timestamp"
        ]
        
        missing_fields = [field for field in expected_fields if field not in weekly_summary]
        
        if not missing_fields:
            logger.info("✅ 周期总结结构完整")
            
            # 验证获胜实验数据结构
            winning_data = weekly_summary["winning_experiment_data"]
            expected_winning_fields = [
                "ab_test_data", "performance_comparison", 
                "prompt_selection_result", "target_agents", "week_number"
            ]
            
            missing_winning_fields = [field for field in expected_winning_fields if field not in winning_data]
            
            if not missing_winning_fields:
                logger.info("✅ 获胜实验数据结构完整")
                return True
            else:
                logger.error(f"❌ 获胜实验数据缺少字段: {missing_winning_fields}")
                return False
        else:
            logger.error(f"❌ 周期总结缺少字段: {missing_fields}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def test_data_flow_consistency():
    """测试数据流的一致性"""
    logger = logging.getLogger(__name__)
    logger.info("测试数据流的一致性")
    
    try:
        config = {"enhanced_shapley_oprp": {}}
        manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
        manager.current_target_agents = ["BeOA", "TAA"]
        
        # 步骤1：收集A/B测试数据
        ab_test_data = manager._collect_ab_test_data(week_number=2)
        original_week_number = ab_test_data.get("week_number")
        original_agents = ab_test_data.get("test_agents", [])
        
        # 步骤2：性能比较
        performance_comparison = manager._compare_ab_test_performance(ab_test_data)
        
        # 步骤3：提示词选择
        prompt_selection_result = manager._select_winning_prompts(performance_comparison)
        
        # 步骤4：生成周期总结
        weekly_summary = manager._finalize_weekly_cycle(
            ab_test_data, performance_comparison, prompt_selection_result
        )
        
        # 验证数据一致性
        winning_data = weekly_summary["winning_experiment_data"]
        
        # 检查周数一致性
        if (winning_data["week_number"] == original_week_number and
            weekly_summary["week_number"] == original_week_number):
            logger.info("✅ 周数数据一致")
        else:
            logger.error("❌ 周数数据不一致")
            return False
        
        # 检查智能体一致性
        if winning_data["ab_test_data"]["test_agents"] == original_agents:
            logger.info("✅ 智能体数据一致")
        else:
            logger.error("❌ 智能体数据不一致")
            return False
        
        # 检查数据完整性
        if (winning_data["ab_test_data"] == ab_test_data and
            winning_data["performance_comparison"] == performance_comparison and
            winning_data["prompt_selection_result"] == prompt_selection_result):
            logger.info("✅ 数据流完整性验证通过")
            return True
        else:
            logger.error("❌ 数据流完整性验证失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 70)
    logger.info("开始OPRO系统获胜数据集成测试")
    logger.info("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # 测试1：模拟A/B测试周完成流程
    logger.info("\n" + "=" * 50)
    logger.info("测试1：模拟A/B测试周完成流程")
    logger.info("=" * 50)
    if simulate_ab_testing_week_completion():
        success_count += 1
    
    # 测试2：周期总结结构完整性
    logger.info("\n" + "=" * 50)
    logger.info("测试2：周期总结结构完整性")
    logger.info("=" * 50)
    if test_weekly_summary_structure():
        success_count += 1
    
    # 测试3：数据流一致性
    logger.info("\n" + "=" * 50)
    logger.info("测试3：数据流一致性")
    logger.info("=" * 50)
    if test_data_flow_consistency():
        success_count += 1
    
    # 总结
    logger.info("\n" + "=" * 70)
    logger.info("测试总结")
    logger.info("=" * 70)
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！OPRO系统获胜数据集成修复成功！")
        logger.info("现在第2周的获胜实验数据应该能够正确传递给迭代Shapley计算")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
