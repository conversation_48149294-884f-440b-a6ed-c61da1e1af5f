2025-07-06 16:01:42,417 - __main__ - INFO - ============================================================
2025-07-06 16:01:42,418 - __main__ - INFO - 开始OPRO系统修复验证测试
2025-07-06 16:01:42,418 - __main__ - INFO - ============================================================
2025-07-06 16:01:42,418 - __main__ - INFO - 
========================================
2025-07-06 16:01:42,418 - __main__ - INFO - 测试1：基本功能测试
2025-07-06 16:01:42,418 - __main__ - INFO - ========================================
2025-07-06 16:01:42,418 - __main__ - INFO - 开始测试增强的周期性OPRO管理器
2025-07-06 16:01:42,418 - __main__ - ERROR - ❌ 测试失败: 'EnhancedOPROConfig' object has no attribute 'get'
2025-07-06 16:01:42,421 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\vscode_project\Multi_Agent_Optimization\Multi_Agent_Optimization\test_opro_fix.py", line 44, in test_enhanced_weekly_opro_manager
    manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vscode_project\Multi_Agent_Optimization\Multi_Agent_Optimization\contribution_assessment\enhanced_weekly_opro_manager.py", line 102, in __init__
    enhanced_config = config.get("enhanced_shapley_oprp", {})
                      ^^^^^^^^^^
AttributeError: 'EnhancedOPROConfig' object has no attribute 'get'

2025-07-06 16:01:42,421 - __main__ - INFO - 
========================================
2025-07-06 16:01:42,421 - __main__ - INFO - 测试2：边界情况测试
2025-07-06 16:01:42,421 - __main__ - INFO - ========================================
2025-07-06 16:01:42,421 - __main__ - INFO - 测试边界情况...
2025-07-06 16:01:42,421 - __main__ - ERROR - ❌ 边界情况测试失败: 'EnhancedOPROConfig' object has no attribute 'get'
2025-07-06 16:01:42,422 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\vscode_project\Multi_Agent_Optimization\Multi_Agent_Optimization\test_opro_fix.py", line 79, in test_edge_cases
    manager = EnhancedWeeklyOPROManager(config=config, logger=logger)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vscode_project\Multi_Agent_Optimization\Multi_Agent_Optimization\contribution_assessment\enhanced_weekly_opro_manager.py", line 102, in __init__
    enhanced_config = config.get("enhanced_shapley_oprp", {})
                      ^^^^^^^^^^
AttributeError: 'EnhancedOPROConfig' object has no attribute 'get'

2025-07-06 16:01:42,422 - __main__ - INFO - 
============================================================
2025-07-06 16:01:42,422 - __main__ - INFO - 测试总结
2025-07-06 16:01:42,422 - __main__ - INFO - ============================================================
2025-07-06 16:01:42,422 - __main__ - INFO - 通过测试: 0/2
2025-07-06 16:01:42,422 - __main__ - ERROR - ❌ 2 个测试失败
