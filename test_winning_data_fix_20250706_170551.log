2025-07-06 17:05:51,774 - __main__ - INFO - ============================================================
2025-07-06 17:05:51,775 - __main__ - INFO - 开始获胜实验数据修复验证测试
2025-07-06 17:05:51,775 - __main__ - INFO - ============================================================
2025-07-06 17:05:51,775 - __main__ - INFO - 
========================================
2025-07-06 17:05:51,775 - __main__ - INFO - 测试1：获胜数据注册和检索
2025-07-06 17:05:51,775 - __main__ - INFO - ========================================
2025-07-06 17:05:51,775 - __main__ - INFO - 开始测试获胜数据注册和检索
2025-07-06 17:05:51,776 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:05:51,777 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:05:51,777 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:05:51,777 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:05:51,777 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:05:51,779 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:05:51,780 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:05:51,780 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:05:51,781 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:05:51,782 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:05:51,782 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:05:51,782 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:05:51,782 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:05:51,783 - __main__ - INFO - 加载历史数据完成: 0 个实验记录
2025-07-06 17:05:51,783 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:05:51,783 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:05:51,784 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:05:51,784 - __main__ - INFO - ✅ 管理器创建成功
2025-07-06 17:05:51,784 - __main__ - INFO - 测试数据准备完成
2025-07-06 17:05:51,784 - __main__ - INFO - 测试注册获胜结果...
2025-07-06 17:05:51,784 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:05:51,784 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_170551
2025-07-06 17:05:51,785 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_170551 - 0 条记录
2025-07-06 17:05:51,785 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_170551
2025-07-06 17:05:51,785 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:05:51,785 - __main__ - INFO - 测试生成周期总结...
2025-07-06 17:05:51,785 - __main__ - INFO - ✅ 周期总结包含获胜实验数据
2025-07-06 17:05:51,786 - __main__ - INFO - 获胜实验数据: {'ab_test_data': {'week_number': 2, 'test_agents': ['BeOA', 'TAA'], 'original_performance': {'BeOA': 0.8, 'TAA': 0.7}, 'optimized_performance': {'BeOA': 0.9, 'TAA': 0.8}, 'data_collected': True}, 'performance_comparison': {'ab_test_week_2': {'config': {'optimized_agents': ['BeOA', 'TAA']}, 'statistical_comparison': {'recommendation': 'optimized', 'improvement': 0.1, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}}, 'prompt_selection_result': {'ab_test_week_2': {'config': {'optimized_agents': ['BeOA', 'TAA']}, 'agent_selections': {'BeOA': {'selected_prompt': 'optimized', 'reason': '优化提示词表现更好 (改进: 0.1000, p值: 0.0100)', 'statistical_data': {'recommendation': 'optimized', 'improvement': 0.1, 'p_value': 0.01}}, 'TAA': {'selected_prompt': 'optimized', 'reason': '优化提示词表现更好 (改进: 0.1000, p值: 0.0100)', 'statistical_data': {'recommendation': 'optimized', 'improvement': 0.1, 'p_value': 0.01}}}, 'overall_recommendation': 'optimized'}}, 'target_agents': [], 'week_number': 2}
2025-07-06 17:05:51,786 - __main__ - INFO - ✅ 获胜实验数据包含所有必需字段
2025-07-06 17:05:51,786 - __main__ - INFO - 
========================================
2025-07-06 17:05:51,786 - __main__ - INFO - 测试2：迭代Shapley数据可用性
2025-07-06 17:05:51,787 - __main__ - INFO - ========================================
2025-07-06 17:05:51,787 - __main__ - INFO - 测试迭代Shapley计算器中的数据可用性...
2025-07-06 17:05:51,787 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:05:51,787 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:05:51,788 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:05:51,788 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:05:51,788 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:05:51,798 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:05:51,799 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:05:51,799 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:05:51,800 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:05:51,800 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:05:51,801 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:05:51,801 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:05:51,801 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:05:51,816 - __main__ - INFO - 加载历史数据完成: 1 个实验记录
2025-07-06 17:05:51,816 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:05:51,816 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:05:51,816 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:05:51,816 - __main__ - INFO - 迭代Shapley计算器中有 1 个实验数据记录
2025-07-06 17:05:51,817 - __main__ - INFO - 获胜数据缓存中有 0 个记录
2025-07-06 17:05:51,817 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:05:51,817 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:05:51,820 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:05:51,821 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:05:51,821 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:05:51,822 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:05:51,822 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-06 17:05:51,823 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:05:51,824 - __main__ - INFO - ✅ 迭代Shapley计算成功
2025-07-06 17:05:51,824 - __main__ - INFO - 
============================================================
2025-07-06 17:05:51,824 - __main__ - INFO - 测试总结
2025-07-06 17:05:51,825 - __main__ - INFO - ============================================================
2025-07-06 17:05:51,825 - __main__ - INFO - 通过测试: 2/2
2025-07-06 17:05:51,825 - __main__ - INFO - 🎉 所有测试通过！获胜实验数据问题修复成功！
