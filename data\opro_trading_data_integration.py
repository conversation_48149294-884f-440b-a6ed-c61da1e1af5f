#!/usr/bin/env python3
"""
OPRO交易数据集成模块

将周期性交易存储管理器集成到OPRO系统中，
提供完整的交易记录存储、A/B测试数据分离、性能指标本地存储功能。

创建时间: 2025-07-06
作者: OPRO系统
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from .weekly_trading_storage_manager import (
    WeeklyTradingStorageManager,
    AgentDecisionRecord,
    WeeklyPerformanceMetrics,
    ABTestTrackData
)

class OPROTradingDataIntegration:
    """
    OPRO交易数据集成器
    
    负责将交易数据存储功能集成到OPRO系统的主要执行流程中
    """
    
    def __init__(self, 
                 base_data_dir: str = "data/trading",
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化OPRO交易数据集成器
        
        参数:
            base_data_dir: 基础数据目录
            config: 配置字典
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        self.config = config or {}
        
        # 初始化存储管理器
        self.storage_manager = WeeklyTradingStorageManager(
            base_data_dir=base_data_dir,
            config=config,
            logger=logger
        )
        
        # 当前活跃的周期信息
        self.current_week_info = {}
        
        self.logger.info("OPRO交易数据集成器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.OPROTradingDataIntegration")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def start_weekly_cycle_integration(self, 
                                     week_number: int,
                                     week_type: str,
                                     start_date: str,
                                     end_date: str,
                                     target_agents: List[str],
                                     metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        开始周期集成
        
        参数:
            week_number: 周次编号
            week_type: 周期类型 ("baseline_operation" 或 "ab_testing")
            start_date: 开始日期
            end_date: 结束日期
            target_agents: 目标智能体列表
            metadata: 元数据
            
        返回:
            周期ID
        """
        try:
            # 准备元数据
            integration_metadata = {
                "target_agents": target_agents,
                "integration_timestamp": datetime.now().isoformat(),
                "opro_system_version": "enhanced_weekly_opro",
                **(metadata or {})
            }
            
            # 开始存储管理器的周期记录
            week_id = self.storage_manager.start_weekly_cycle(
                week_number=week_number,
                week_type=week_type,
                start_date=start_date,
                end_date=end_date,
                metadata=integration_metadata
            )
            
            # 保存当前周期信息
            self.current_week_info = {
                "week_id": week_id,
                "week_number": week_number,
                "week_type": week_type,
                "start_date": start_date,
                "end_date": end_date,
                "target_agents": target_agents,
                "started_at": datetime.now().isoformat()
            }
            
            self.logger.info(f"开始OPRO周期集成: {week_id}")
            return week_id
            
        except Exception as e:
            self.logger.error(f"开始周期集成失败: {e}")
            raise
    
    def record_daily_trading_decisions(self, 
                                     daily_results: Dict[str, Any],
                                     trading_date: str,
                                     track_type: str = "baseline") -> bool:
        """
        记录每日交易决策
        
        参数:
            daily_results: 每日交易结果
            trading_date: 交易日期
            track_type: 轨道类型 ("baseline", "original", "optimized")
            
        返回:
            是否记录成功
        """
        try:
            if not self.current_week_info:
                self.logger.error("未开始周期集成，请先调用start_weekly_cycle_integration()")
                return False
            
            # 从每日结果中提取智能体决策
            success_count = 0
            
            # 处理评估结果中的智能体数据
            if "assessment_result" in daily_results:
                assessment_result = daily_results["assessment_result"]
                
                # 提取Shapley值作为智能体贡献
                shapley_values = assessment_result.get("shapley_values", {})
                
                # 提取智能体决策（如果有的话）
                agent_decisions = assessment_result.get("agent_decisions", {})
                
                # 提取市场条件
                market_conditions = assessment_result.get("market_conditions", {})
                
                for agent_id in self.current_week_info["target_agents"]:
                    # 创建智能体决策记录
                    decision_record = self._create_agent_decision_from_results(
                        agent_id=agent_id,
                        daily_results=daily_results,
                        trading_date=trading_date,
                        shapley_values=shapley_values,
                        agent_decisions=agent_decisions,
                        market_conditions=market_conditions
                    )
                    
                    if decision_record:
                        if self.storage_manager.record_agent_decision(decision_record, track_type):
                            success_count += 1
            
            self.logger.debug(f"记录每日交易决策: {trading_date} - {success_count}个智能体")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"记录每日交易决策失败: {e}")
            return False
    
    def _create_agent_decision_from_results(self,
                                          agent_id: str,
                                          daily_results: Dict[str, Any],
                                          trading_date: str,
                                          shapley_values: Dict[str, float],
                                          agent_decisions: Dict[str, Any],
                                          market_conditions: Dict[str, Any]) -> Optional[AgentDecisionRecord]:
        """从结果创建智能体决策记录"""
        try:
            # 获取智能体的Shapley值作为决策依据
            shapley_value = shapley_values.get(agent_id, 0.0)
            
            # 基于Shapley值推断决策类型
            if shapley_value > 0.02:
                decision_type = "buy"
                decision_reason = f"正向Shapley值({shapley_value:.4f})表明该智能体对系统有积极贡献，建议买入"
            elif shapley_value < -0.02:
                decision_type = "sell"
                decision_reason = f"负向Shapley值({shapley_value:.4f})表明该智能体对系统有负面影响，建议卖出"
            else:
                decision_type = "hold"
                decision_reason = f"Shapley值({shapley_value:.4f})接近中性，建议持有"
            
            # 构建分析过程
            analysis_process = f"基于Shapley值分析的智能体{agent_id}决策过程：\n"
            analysis_process += f"1. 计算得到Shapley值: {shapley_value:.4f}\n"
            analysis_process += f"2. 根据阈值判断决策类型: {decision_type}\n"
            analysis_process += f"3. 考虑市场条件和风险因素\n"
            analysis_process += f"4. 最终决策: {decision_type}"
            
            # 计算置信度分数
            confidence_score = min(abs(shapley_value) * 10, 1.0)  # 基于Shapley值的绝对值
            
            # 获取智能体特定的决策数据（如果有）
            agent_specific_decision = agent_decisions.get(agent_id, {})
            
            return AgentDecisionRecord(
                agent_id=agent_id,
                decision_type=decision_type,
                decision_reason=decision_reason,
                analysis_process=analysis_process,
                confidence_score=confidence_score,
                timestamp=f"{trading_date}T{datetime.now().strftime('%H:%M:%S')}",
                market_conditions=market_conditions,
                input_data={
                    "shapley_value": shapley_value,
                    "daily_results_summary": {
                        "success": daily_results.get("success", False),
                        "execution_time": daily_results.get("execution_time", 0)
                    },
                    "agent_specific_data": agent_specific_decision
                },
                metadata={
                    "data_source": "opro_daily_assessment",
                    "week_number": self.current_week_info["week_number"],
                    "week_type": self.current_week_info["week_type"]
                }
            )
            
        except Exception as e:
            self.logger.error(f"创建智能体决策记录失败: {e}")
            return None
    
    def record_weekly_performance_integration(self, 
                                            weekly_results: Dict[str, Any],
                                            track_type: str = "baseline") -> bool:
        """
        记录周期性能集成
        
        参数:
            weekly_results: 周期结果
            track_type: 轨道类型
            
        返回:
            是否记录成功
        """
        try:
            if not self.current_week_info:
                self.logger.error("未开始周期集成")
                return False
            
            # 从周期结果中提取性能指标
            performance_metrics = self._create_performance_metrics_from_results(
                weekly_results, track_type
            )
            
            if performance_metrics:
                return self.storage_manager.record_weekly_performance(
                    performance_metrics, track_type
                )
            
            return False
            
        except Exception as e:
            self.logger.error(f"记录周期性能集成失败: {e}")
            return False
    
    def _create_performance_metrics_from_results(self, 
                                               weekly_results: Dict[str, Any],
                                               track_type: str) -> Optional[WeeklyPerformanceMetrics]:
        """从结果创建性能指标"""
        try:
            # 提取基本性能数据
            trading_stats = weekly_results.get("trading_statistics", {})
            shapley_results = weekly_results.get("shapley_results", {})
            
            # 计算总收益率（基于Shapley值的模拟）
            total_return = sum(shapley_results.get("shapley_values", {}).values()) * 100
            
            # 计算夏普率（模拟）
            sharpe_ratio = total_return / max(abs(total_return) * 0.1, 0.01)  # 简化计算
            
            # 计算最大回撤（模拟）
            max_drawdown = abs(min(shapley_results.get("shapley_values", {}).values(), default=0)) * 50
            
            # 计算波动率（模拟）
            shapley_values = list(shapley_results.get("shapley_values", {}).values())
            volatility = (max(shapley_values, default=0) - min(shapley_values, default=0)) * 20
            
            # 计算胜率（基于正向Shapley值的比例）
            positive_agents = sum(1 for v in shapley_values if v > 0)
            win_rate = positive_agents / max(len(shapley_values), 1)
            
            return WeeklyPerformanceMetrics(
                week_number=self.current_week_info["week_number"],
                start_date=self.current_week_info["start_date"],
                end_date=self.current_week_info["end_date"],
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                volatility=volatility,
                win_rate=win_rate,
                total_trades=len(shapley_values),
                agent_contributions=shapley_results.get("shapley_values", {}),
                risk_metrics={
                    "value_at_risk": max_drawdown * 0.8,
                    "expected_shortfall": max_drawdown * 1.2,
                    "beta": 1.0,  # 默认值
                    "alpha": total_return - sharpe_ratio
                },
                metadata={
                    "track_type": track_type,
                    "calculation_method": "shapley_based_simulation",
                    "data_quality": "simulated",
                    "week_type": self.current_week_info["week_type"]
                }
            )
            
        except Exception as e:
            self.logger.error(f"创建性能指标失败: {e}")
            return None
    
    def finish_weekly_cycle_integration(self, 
                                      weekly_summary: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        完成周期集成
        
        参数:
            weekly_summary: 周期总结
            
        返回:
            完成结果
        """
        try:
            if not self.current_week_info:
                self.logger.error("未开始周期集成")
                return {"success": False, "error": "未开始周期集成"}
            
            # 准备总结数据
            integration_summary = {
                "week_info": self.current_week_info,
                "weekly_summary": weekly_summary or {},
                "integration_completed_at": datetime.now().isoformat()
            }
            
            # 完成存储管理器的周期记录
            result = self.storage_manager.finish_weekly_cycle(integration_summary)
            
            # 重置当前周期信息
            self.current_week_info = {}
            
            self.logger.info(f"完成OPRO周期集成: 第{result.get('week_number')}周")
            return result
            
        except Exception as e:
            self.logger.error(f"完成周期集成失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_winning_experiment_data_integration(self, week_number: int) -> Dict[str, Any]:
        """
        获取获胜实验数据集成（为迭代Shapley计算提供）
        
        参数:
            week_number: 周次编号
            
        返回:
            获胜实验数据
        """
        try:
            return self.storage_manager.get_winning_experiment_data(week_number)
            
        except Exception as e:
            self.logger.error(f"获取获胜实验数据集成失败: {e}")
            return {}
    
    def export_integration_summary(self, week_number: int) -> str:
        """
        导出集成总结
        
        参数:
            week_number: 周次编号
            
        返回:
            导出文件路径
        """
        try:
            return self.storage_manager.export_weekly_summary(week_number, "json")
            
        except Exception as e:
            self.logger.error(f"导出集成总结失败: {e}")
            return ""
