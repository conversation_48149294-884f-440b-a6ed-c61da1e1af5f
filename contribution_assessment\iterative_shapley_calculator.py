#!/usr/bin/env python3
"""
迭代Shapley值计算器 (Iterative Shapley Calculator)

本模块实现了增强的Shapley值计算系统，支持：
1. 基于获胜实验数据的迭代计算
2. 历史数据跟踪和管理
3. 提示词优化结果集成
4. 性能结果记录和分析
5. 数据质量验证和清理

主要功能：
- 只使用A/B测试获胜的提示词数据进行Shapley计算
- 维护完整的历史跟踪记录
- 支持增量更新和回滚机制
- 提供数据质量评估和异常检测

作者: AI Assistant
创建时间: 2025-07-06
"""

import os
import json
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import copy

from .shapley_calculator import ShapleyCalculator
from .enhanced_shapley_storage_manager import EnhancedShapleyStorageManager

@dataclass
class ExperimentDataRecord:
    """实验数据记录"""
    experiment_id: str
    agent_id: str
    prompt_type: str  # "original" or "optimized"
    prompt_hash: str
    performance_data: Dict[str, Any]
    is_winning: bool
    experiment_date: str
    metadata: Dict[str, Any]

@dataclass
class IterativeCalculationConfig:
    """迭代计算配置"""
    use_only_winning_data: bool = True
    min_data_points_per_agent: int = 5
    data_quality_threshold: float = 0.8
    enable_historical_tracking: bool = True
    enable_data_validation: bool = True
    max_historical_weeks: int = 12
    outlier_detection_enabled: bool = True
    outlier_threshold: float = 2.0

class IterativeShapleyCalculator:
    """
    迭代Shapley值计算器
    
    基于获胜实验数据进行Shapley值计算，确保只使用经过A/B测试验证的
    最优提示词数据，维护完整的历史跟踪和数据质量管理
    """
    
    def __init__(self, 
                 base_data_dir: str = "data/trading",
                 config: Optional[IterativeCalculationConfig] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化迭代Shapley值计算器
        
        参数:
            base_data_dir: 基础数据目录
            config: 迭代计算配置
            logger: 日志记录器
        """
        self.base_data_dir = Path(base_data_dir)
        self.config = config or IterativeCalculationConfig()
        self.logger = logger or self._create_default_logger()
        
        # 初始化核心组件
        self.shapley_calculator = ShapleyCalculator(logger=self.logger)
        self.storage_manager = EnhancedShapleyStorageManager(
            base_data_dir=str(base_data_dir),
            logger=self.logger
        )
        
        # 数据管理
        self.experiment_data_records = {}  # {experiment_id: List[ExperimentDataRecord]}
        self.winning_data_cache = {}       # {agent_id: List[ExperimentDataRecord]}
        self.historical_shapley_values = {} # {date: {agent_id: shapley_value}}
        self.data_quality_metrics = {}     # 数据质量指标
        
        # 创建必要的目录结构
        self._setup_directory_structure()
        
        # 加载历史数据
        self._load_historical_data()
        
        self.logger.info("迭代Shapley值计算器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.IterativeShapleyCalculator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _setup_directory_structure(self):
        """设置目录结构"""
        directories = [
            self.base_data_dir / "iterative_shapley",
            self.base_data_dir / "experiment_data_records",
            self.base_data_dir / "winning_data_cache",
            self.base_data_dir / "historical_shapley_values",
            self.base_data_dir / "data_quality_reports"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _load_historical_data(self):
        """加载历史数据"""
        try:
            # 加载实验数据记录
            records_dir = self.base_data_dir / "experiment_data_records"
            if records_dir.exists():
                for record_file in records_dir.glob("*.json"):
                    with open(record_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        experiment_id = data.get("experiment_id")
                        if experiment_id:
                            records = [ExperimentDataRecord(**record) for record in data.get("records", [])]
                            self.experiment_data_records[experiment_id] = records
            
            # 加载获胜数据缓存
            cache_dir = self.base_data_dir / "winning_data_cache"
            if cache_dir.exists():
                for cache_file in cache_dir.glob("*.json"):
                    agent_id = cache_file.stem
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        records = [ExperimentDataRecord(**record) for record in data]
                        self.winning_data_cache[agent_id] = records
            
            # 加载历史Shapley值
            history_file = self.base_data_dir / "historical_shapley_values" / "shapley_history.json"
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.historical_shapley_values = json.load(f)
            
            self.logger.info(f"加载历史数据完成: {len(self.experiment_data_records)} 个实验记录")
            
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
    
    def register_experiment_results(self, 
                                  experiment_id: str,
                                  ab_test_results: Dict[str, Any],
                                  performance_data: Dict[str, Any]) -> bool:
        """
        注册实验结果
        
        参数:
            experiment_id: 实验ID
            ab_test_results: A/B测试结果
            performance_data: 性能数据
            
        返回:
            是否注册成功
        """
        try:
            self.logger.info(f"📝 注册实验结果: {experiment_id}")
            
            experiment_records = []
            
            # 处理每个智能体的实验结果
            for agent_id, agent_results in ab_test_results.items():
                if not isinstance(agent_results, dict):
                    continue
                
                agent_selections = agent_results.get("agent_selections", {})
                
                for agent_selection_id, selection_info in agent_selections.items():
                    # 创建实验数据记录
                    selected_prompt = selection_info.get("selected_prompt", "original")
                    is_winning = selected_prompt == "optimized"
                    
                    # 生成提示词哈希
                    prompt_content = selection_info.get("prompt_content", f"{selected_prompt}_prompt_{agent_id}")
                    prompt_hash = hashlib.md5(prompt_content.encode('utf-8')).hexdigest()
                    
                    # 提取性能数据
                    agent_performance = performance_data.get(agent_id, {})
                    
                    record = ExperimentDataRecord(
                        experiment_id=experiment_id,
                        agent_id=agent_id,
                        prompt_type=selected_prompt,
                        prompt_hash=prompt_hash,
                        performance_data=agent_performance,
                        is_winning=is_winning,
                        experiment_date=datetime.now().strftime("%Y-%m-%d"),
                        metadata={
                            "selection_reason": selection_info.get("reason", ""),
                            "statistical_data": selection_info.get("statistical_data", {}),
                            "experiment_config": ab_test_results.get("config", {})
                        }
                    )
                    
                    experiment_records.append(record)
            
            # 保存实验记录
            self.experiment_data_records[experiment_id] = experiment_records
            self._save_experiment_records(experiment_id, experiment_records)
            
            # 更新获胜数据缓存
            self._update_winning_data_cache(experiment_records)
            
            self.logger.info(f"✅ 实验结果注册完成: {experiment_id} - {len(experiment_records)} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 注册实验结果失败: {e}")
            return False
    
    def _save_experiment_records(self, experiment_id: str, records: List[ExperimentDataRecord]):
        """保存实验记录"""
        records_dir = self.base_data_dir / "experiment_data_records"
        record_file = records_dir / f"{experiment_id}.json"
        
        data = {
            "experiment_id": experiment_id,
            "timestamp": datetime.now().isoformat(),
            "records": [asdict(record) for record in records]
        }
        
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _update_winning_data_cache(self, records: List[ExperimentDataRecord]):
        """更新获胜数据缓存"""
        for record in records:
            if record.is_winning or not self.config.use_only_winning_data:
                agent_id = record.agent_id
                
                if agent_id not in self.winning_data_cache:
                    self.winning_data_cache[agent_id] = []
                
                # 避免重复记录
                existing_hashes = {r.prompt_hash for r in self.winning_data_cache[agent_id]}
                if record.prompt_hash not in existing_hashes:
                    self.winning_data_cache[agent_id].append(record)
                    
                    # 限制缓存大小
                    max_cache_size = self.config.max_historical_weeks * 2
                    if len(self.winning_data_cache[agent_id]) > max_cache_size:
                        # 按日期排序，保留最新的记录
                        self.winning_data_cache[agent_id].sort(
                            key=lambda x: x.experiment_date, reverse=True
                        )
                        self.winning_data_cache[agent_id] = self.winning_data_cache[agent_id][:max_cache_size]
        
        # 保存更新的缓存
        self._save_winning_data_cache()
    
    def _save_winning_data_cache(self):
        """保存获胜数据缓存"""
        cache_dir = self.base_data_dir / "winning_data_cache"
        
        for agent_id, records in self.winning_data_cache.items():
            cache_file = cache_dir / f"{agent_id}.json"
            data = [asdict(record) for record in records]
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
    
    def calculate_iterative_shapley_values(self, 
                                         target_agents: List[str],
                                         coalition_values: Optional[Dict[frozenset, float]] = None,
                                         use_cached_data: bool = True) -> Dict[str, Any]:
        """
        计算迭代Shapley值
        
        参数:
            target_agents: 目标智能体列表
            coalition_values: 联盟值（如果提供则使用，否则从缓存数据构建）
            use_cached_data: 是否使用缓存的获胜数据
            
        返回:
            包含Shapley值和计算元数据的结果
        """
        try:
            self.logger.info(f"🧮 开始迭代Shapley值计算: {target_agents}")
            
            # 数据质量检查
            quality_report = self._assess_data_quality(target_agents)
            if quality_report["overall_quality"] < self.config.data_quality_threshold:
                self.logger.warning(f"⚠️ 数据质量较低: {quality_report['overall_quality']:.3f}")
            
            # 构建联盟值（如果未提供）
            if coalition_values is None:
                coalition_values = self._build_coalition_values_from_cache(target_agents)
            
            # 数据预处理和清理
            cleaned_coalition_values = self._clean_coalition_data(coalition_values)
            
            # 执行Shapley值计算
            shapley_values = self.shapley_calculator.calculate(
                agents=target_agents,
                coalition_values=cleaned_coalition_values
            )
            
            # 记录历史Shapley值
            current_date = datetime.now().strftime("%Y-%m-%d")
            self.historical_shapley_values[current_date] = shapley_values
            self._save_historical_shapley_values()
            
            # 生成计算元数据
            calculation_metadata = {
                "calculation_date": current_date,
                "target_agents": target_agents,
                "data_source": "winning_experiments" if use_cached_data else "provided_coalition_values",
                "data_quality_report": quality_report,
                "coalition_count": len(cleaned_coalition_values),
                "config": asdict(self.config)
            }
            
            result = {
                "shapley_values": shapley_values,
                "calculation_metadata": calculation_metadata,
                "data_quality_report": quality_report,
                "success": True
            }
            
            self.logger.info(f"✅ 迭代Shapley值计算完成")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 迭代Shapley值计算失败: {e}")
            return {
                "shapley_values": {agent: 0.0 for agent in target_agents},
                "calculation_metadata": {"error": str(e)},
                "success": False
            }

    def _assess_data_quality(self, target_agents: List[str]) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            quality_metrics = {
                "agent_data_coverage": {},
                "data_freshness": {},
                "data_consistency": {},
                "overall_quality": 0.0
            }

            total_quality_score = 0.0

            for agent_id in target_agents:
                agent_records = self.winning_data_cache.get(agent_id, [])

                # 数据覆盖率
                coverage_score = min(len(agent_records) / self.config.min_data_points_per_agent, 1.0)
                quality_metrics["agent_data_coverage"][agent_id] = coverage_score

                # 数据新鲜度（最近数据的时间）
                if agent_records:
                    latest_date = max(record.experiment_date for record in agent_records)
                    days_since_latest = (datetime.now() - datetime.strptime(latest_date, "%Y-%m-%d")).days
                    freshness_score = max(0.0, 1.0 - days_since_latest / 30.0)  # 30天内为满分
                else:
                    freshness_score = 0.0

                quality_metrics["data_freshness"][agent_id] = freshness_score

                # 数据一致性（性能数据的变异系数）
                if len(agent_records) > 1:
                    performances = []
                    for record in agent_records:
                        perf_data = record.performance_data
                        if isinstance(perf_data, dict) and "sharpe_ratio" in perf_data:
                            performances.append(perf_data["sharpe_ratio"])

                    if len(performances) > 1:
                        mean_perf = np.mean(performances)
                        std_perf = np.std(performances)
                        cv = std_perf / (abs(mean_perf) + 1e-8)  # 变异系数
                        consistency_score = max(0.0, 1.0 - cv)  # 变异系数越小，一致性越好
                    else:
                        consistency_score = 0.5
                else:
                    consistency_score = 0.0

                quality_metrics["data_consistency"][agent_id] = consistency_score

                # 计算智能体总体质量分数
                agent_quality = (coverage_score + freshness_score + consistency_score) / 3.0
                total_quality_score += agent_quality

            # 计算整体质量分数
            quality_metrics["overall_quality"] = total_quality_score / len(target_agents) if target_agents else 0.0

            return quality_metrics

        except Exception as e:
            self.logger.error(f"数据质量评估失败: {e}")
            return {
                "agent_data_coverage": {},
                "data_freshness": {},
                "data_consistency": {},
                "overall_quality": 0.0,
                "error": str(e)
            }

    def _build_coalition_values_from_cache(self, target_agents: List[str]) -> Dict[frozenset, float]:
        """从缓存数据构建联盟值"""
        try:
            coalition_values = {}

            # 生成所有可能的联盟
            from itertools import combinations

            for r in range(len(target_agents) + 1):
                for coalition_tuple in combinations(target_agents, r):
                    coalition = frozenset(coalition_tuple)

                    # 计算联盟的平均性能
                    coalition_performance = self._calculate_coalition_performance(coalition)
                    coalition_values[coalition] = coalition_performance

            self.logger.info(f"从缓存数据构建了 {len(coalition_values)} 个联盟值")
            return coalition_values

        except Exception as e:
            self.logger.error(f"构建联盟值失败: {e}")
            return {}

    def _calculate_coalition_performance(self, coalition: frozenset) -> float:
        """计算联盟性能"""
        try:
            if not coalition:
                return 0.0

            total_performance = 0.0
            valid_agents = 0

            for agent_id in coalition:
                agent_records = self.winning_data_cache.get(agent_id, [])

                if agent_records:
                    # 使用最近的获胜记录
                    recent_records = sorted(agent_records, key=lambda x: x.experiment_date, reverse=True)[:3]

                    agent_performances = []
                    for record in recent_records:
                        perf_data = record.performance_data
                        if isinstance(perf_data, dict) and "sharpe_ratio" in perf_data:
                            agent_performances.append(perf_data["sharpe_ratio"])

                    if agent_performances:
                        # 异常值检测和清理
                        if self.config.outlier_detection_enabled:
                            agent_performances = self._remove_outliers(agent_performances)

                        if agent_performances:
                            agent_avg_performance = np.mean(agent_performances)
                            total_performance += agent_avg_performance
                            valid_agents += 1
                else:
                    # 如果没有历史数据，使用基于智能体类型的默认性能值
                    default_performance = self._get_default_agent_performance(agent_id)
                    total_performance += default_performance
                    valid_agents += 1

            # 计算联盟平均性能
            if valid_agents > 0:
                coalition_performance = total_performance / valid_agents

                # 联盟协同效应（简化模型）
                if len(coalition) > 1:
                    synergy_factor = 1.0 + 0.1 * (len(coalition) - 1)  # 每增加一个智能体，增加10%协同效应
                    coalition_performance *= synergy_factor

                return float(coalition_performance)
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"计算联盟性能失败: {e}")
            return 0.0

    def _get_default_agent_performance(self, agent_id: str) -> float:
        """获取智能体的默认性能值（当没有历史数据时使用）"""
        # 基于智能体类型的默认Sharpe比率
        default_performances = {
            "NAA": 0.15,   # 新闻分析智能体
            "TAA": 0.12,   # 技术分析智能体
            "FAA": 0.18,   # 基本面分析智能体
            "BOA": 0.10,   # 看涨观点智能体
            "BeOA": 0.08,  # 看跌观点智能体
            "NOA": 0.14,   # 中性观点智能体
            "TRA": 0.20    # 交易智能体
        }

        # 添加一些随机性以模拟不同的表现
        base_performance = default_performances.get(agent_id, 0.12)

        # 添加±20%的随机变化
        import random
        random.seed(hash(agent_id) % 1000)  # 使用智能体ID作为种子，确保一致性
        variation = random.uniform(-0.2, 0.2)

        return base_performance * (1 + variation)

    def _remove_outliers(self, data: List[float]) -> List[float]:
        """移除异常值"""
        try:
            if len(data) < 3:
                return data

            data_array = np.array(data)
            mean = np.mean(data_array)
            std = np.std(data_array)

            # 使用Z-score方法检测异常值
            z_scores = np.abs((data_array - mean) / (std + 1e-8))

            # 保留Z-score小于阈值的数据
            filtered_data = data_array[z_scores < self.config.outlier_threshold]

            return filtered_data.tolist()

        except Exception:
            return data

    def _clean_coalition_data(self, coalition_values: Dict[frozenset, float]) -> Dict[frozenset, float]:
        """清理联盟数据"""
        try:
            cleaned_values = {}

            for coalition, value in coalition_values.items():
                # 检查数值有效性
                if isinstance(value, (int, float)) and not np.isnan(value) and not np.isinf(value):
                    cleaned_values[coalition] = float(value)
                else:
                    # 使用默认值替换无效数据
                    cleaned_values[coalition] = 0.0
                    self.logger.warning(f"联盟 {coalition} 的值无效，已替换为0.0")

            return cleaned_values

        except Exception as e:
            self.logger.error(f"清理联盟数据失败: {e}")
            return coalition_values

    def _save_historical_shapley_values(self):
        """保存历史Shapley值"""
        try:
            history_file = self.base_data_dir / "historical_shapley_values" / "shapley_history.json"

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.historical_shapley_values, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存历史Shapley值失败: {e}")

    def get_historical_shapley_trends(self,
                                    agent_id: str,
                                    days: int = 30) -> Dict[str, Any]:
        """获取历史Shapley值趋势"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            trends = {
                "agent_id": agent_id,
                "period": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                "data_points": [],
                "trend_analysis": {}
            }

            # 收集时间范围内的数据点
            for date_str, shapley_values in self.historical_shapley_values.items():
                try:
                    date = datetime.strptime(date_str, "%Y-%m-%d")
                    if start_date <= date <= end_date and agent_id in shapley_values:
                        trends["data_points"].append({
                            "date": date_str,
                            "shapley_value": shapley_values[agent_id]
                        })
                except ValueError:
                    continue

            # 趋势分析
            if len(trends["data_points"]) >= 2:
                values = [point["shapley_value"] for point in trends["data_points"]]

                trends["trend_analysis"] = {
                    "mean_value": np.mean(values),
                    "std_deviation": np.std(values),
                    "min_value": np.min(values),
                    "max_value": np.max(values),
                    "trend_direction": "increasing" if values[-1] > values[0] else "decreasing",
                    "volatility": np.std(values) / (abs(np.mean(values)) + 1e-8)
                }

            return trends

        except Exception as e:
            self.logger.error(f"获取历史趋势失败: {e}")
            return {"error": str(e)}

    def get_data_quality_report(self, target_agents: List[str]) -> Dict[str, Any]:
        """获取数据质量报告"""
        try:
            quality_report = self._assess_data_quality(target_agents)

            # 添加详细统计信息
            detailed_stats = {}
            for agent_id in target_agents:
                agent_records = self.winning_data_cache.get(agent_id, [])

                detailed_stats[agent_id] = {
                    "total_records": len(agent_records),
                    "winning_records": sum(1 for r in agent_records if r.is_winning),
                    "date_range": {
                        "earliest": min(r.experiment_date for r in agent_records) if agent_records else None,
                        "latest": max(r.experiment_date for r in agent_records) if agent_records else None
                    },
                    "unique_prompts": len(set(r.prompt_hash for r in agent_records))
                }

            quality_report["detailed_statistics"] = detailed_stats
            quality_report["report_generated_at"] = datetime.now().isoformat()

            # 保存质量报告
            report_file = self.base_data_dir / "data_quality_reports" / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(quality_report, f, ensure_ascii=False, indent=2)

            return quality_report

        except Exception as e:
            self.logger.error(f"生成数据质量报告失败: {e}")
            return {"error": str(e)}

    def cleanup_old_data(self, days_to_keep: int = 90) -> Dict[str, Any]:
        """清理旧数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_date.strftime("%Y-%m-%d")

            cleanup_stats = {
                "experiment_records_removed": 0,
                "cache_records_removed": 0,
                "historical_entries_removed": 0
            }

            # 清理实验记录
            for experiment_id, records in list(self.experiment_data_records.items()):
                records_to_keep = [r for r in records if r.experiment_date >= cutoff_str]
                if len(records_to_keep) < len(records):
                    cleanup_stats["experiment_records_removed"] += len(records) - len(records_to_keep)
                    if records_to_keep:
                        self.experiment_data_records[experiment_id] = records_to_keep
                    else:
                        del self.experiment_data_records[experiment_id]

            # 清理获胜数据缓存
            for agent_id, records in self.winning_data_cache.items():
                records_to_keep = [r for r in records if r.experiment_date >= cutoff_str]
                cleanup_stats["cache_records_removed"] += len(records) - len(records_to_keep)
                self.winning_data_cache[agent_id] = records_to_keep

            # 清理历史Shapley值
            dates_to_remove = [date for date in self.historical_shapley_values.keys() if date < cutoff_str]
            for date in dates_to_remove:
                del self.historical_shapley_values[date]
                cleanup_stats["historical_entries_removed"] += 1

            # 保存清理后的数据
            self._save_experiment_records_all()
            self._save_winning_data_cache()
            self._save_historical_shapley_values()

            self.logger.info(f"数据清理完成: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            self.logger.error(f"数据清理失败: {e}")
            return {"error": str(e)}

    def _save_experiment_records_all(self):
        """保存所有实验记录"""
        for experiment_id, records in self.experiment_data_records.items():
            self._save_experiment_records(experiment_id, records)
