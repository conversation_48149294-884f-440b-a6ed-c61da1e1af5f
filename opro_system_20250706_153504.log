2025-07-06 15:35:04,265 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,266 - __main__ - INFO - OPRO系统启动
2025-07-06 15:35:04,266 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,266 - __main__ - INFO - 运行模式: integrated
2025-07-06 15:35:04,266 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 15:35:04,266 - __main__ - INFO - OPRO启用: True
2025-07-06 15:35:04,266 - __main__ - INFO - 数据存储启用: True
2025-07-06 15:35:04,266 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 15:35:04,268 - __main__ - INFO - 初始化系统...
2025-07-06 15:35:04,268 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 15:35:04,268 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 15:35:04,269 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 15:35:04,269 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 15:35:04,269 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 15:35:04,269 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 15:35:04,269 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 15:35:04,270 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 15:35:04,270 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 15:35:04,270 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 15:35:04,270 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 15:35:04,271 - __main__ - INFO - 数据库初始化完成
2025-07-06 15:35:04,272 - __main__ - INFO - 自动备份线程已启动
2025-07-06 15:35:04,272 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 15:35:04,272 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 15:35:04,272 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 15:35:04,277 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 15:35:04,277 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 15:35:04,277 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 15:35:04,283 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 15:35:04,283 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 15:35:04,283 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 15:35:04,284 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 15:35:04,284 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 15:35:04,285 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 15:35:04,285 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 15:35:04,285 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 15:35:04,285 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 15:35:04,301 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 15:35:04,301 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 15:35:04,301 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 15:35:04,301 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 15:35:04,302 - contribution_assessment.weekly_opro_manager - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 15:35:04,305 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 15:35:04,306 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 15:35:04,307 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 15:35:04,308 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 15:35:04,308 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 15:35:04,310 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 15:35:04,310 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 15:35:04,310 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 15:35:04,311 - __main__ - INFO - 加载历史数据完成: 0 个实验记录
2025-07-06 15:35:04,311 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 15:35:04,311 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 15:35:04,311 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 15:35:04,311 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 15:35:04,311 - __main__ - INFO - 系统初始化完成
2025-07-06 15:35:04,312 - __main__ - INFO - ================================================================================
2025-07-06 15:35:04,312 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 15:35:04,312 - __main__ - INFO - ================================================================================
2025-07-06 15:35:04,312 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-01-31
2025-07-06 15:35:04,312 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,312 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 15:35:04,312 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,313 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-31
2025-07-06 15:35:04,313 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 15:35:04,313 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 15:35:04,313 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 15:35:04,314 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-31
2025-07-06 15:35:04,314 - __main__ - INFO - 📊 总交易日数: 23
2025-07-06 15:35:04,314 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 15:35:04,314 - __main__ - INFO - 🗓️  最后交易日: 2025-01-31
2025-07-06 15:35:04,314 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 15:35:04,315 - __main__ - INFO - 📊 生成了 3 个7天周期
2025-07-06 15:35:04,315 - __main__ - INFO -    - 基线运行周: 2 个
2025-07-06 15:35:04,315 - __main__ - INFO -    - A/B测试周: 1 个
2025-07-06 15:35:04,315 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 15:35:04,315 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 15:35:04,315 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 15:35:04,316 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 15:35:04,316 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 15:35:04,316 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 15:35:04,316 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 15:35:04,316 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 15:35:04,317 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 15:35:04,317 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 15:35:04,317 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 15:35:04,317 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 15:35:04,317 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 15:35:04,318 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 15:35:04,318 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 15:35:04,318 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 15:35:04,319 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 15:35:04,320 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 15:35:04,321 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 15:35:04,321 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 15:35:04,321 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 15:35:04,322 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 15:35:04,322 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 15:35:04,323 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 15:35:04,323 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 15:35:04,323 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 15:35:04,323 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 15:35:04,324 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 15:35:04,324 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 15:35:04,324 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 15:35:04,324 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 15:35:04,324 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 15:35:04,325 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 15:35:04,325 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 15:35:04,325 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 15:35:04,325 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 15:35:04,325 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 15:35:04,326 - __main__ - INFO - Shapley值计算完成，耗时 0.005s
2025-07-06 15:35:04,326 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 15:35:04,327 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['NAA', 'TAA']
2025-07-06 15:35:04,327 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['NAA', 'TAA']
2025-07-06 15:35:04,327 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NAA', 'TAA']
2025-07-06 15:35:04,327 - contribution_assessment.weekly_opro_manager - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 15:35:04,327 - contribution_assessment.weekly_opro_manager - ERROR - 代理 NAA 优化失败: 'dict' object has no attribute 'optimize_agent_prompt'
2025-07-06 15:35:04,327 - contribution_assessment.weekly_opro_manager - ERROR - 代理 TAA 优化失败: 'dict' object has no attribute 'optimize_agent_prompt'
2025-07-06 15:35:04,363 - contribution_assessment.weekly_opro_manager - INFO - 每周优化循环完成 - 周期: 2025-01-01
2025-07-06 15:35:04,364 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 15:35:04,364 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 15:35:04,364 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 15:35:04,364 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-09
2025-07-06 15:35:04,365 - __main__ - INFO - 🆕 开始第2周 - A/B测试阶段: 2025-01-09
2025-07-06 15:35:04,365 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第1/7天
2025-07-06 15:35:04,365 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 15:35:04,365 - __main__ - INFO - 🔄 开始第 2 周（ab_testing）: 2025-01-10 到 2025-01-20
2025-07-06 15:35:04,366 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-10
2025-07-06 15:35:04,366 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第2/7天
2025-07-06 15:35:04,366 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-13
2025-07-06 15:35:04,366 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第5/7天
2025-07-06 15:35:04,367 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-14
2025-07-06 15:35:04,367 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第6/7天
2025-07-06 15:35:04,367 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-15
2025-07-06 15:35:04,367 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第7/7天
2025-07-06 15:35:04,368 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 15:35:04,368 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 15:35:04,368 - __main__ - ERROR - 7天连续优化循环执行失败: 'bool' object is not subscriptable
2025-07-06 15:35:04,368 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-16
2025-07-06 15:35:04,369 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-17
2025-07-06 15:35:04,369 - __main__ - INFO - 🆕 开始第3周 - 基线运行阶段: 2025-01-17
2025-07-06 15:35:04,370 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第1/7天
2025-07-06 15:35:04,370 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-20
2025-07-06 15:35:04,371 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第4/7天
2025-07-06 15:35:04,371 - __main__ - INFO - ✅ 第 2 周完成: in_progress
2025-07-06 15:35:04,371 - __main__ - INFO - 🔄 开始第 3 周（baseline_operation）: 2025-01-21 到 2025-01-29
2025-07-06 15:35:04,372 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-21
2025-07-06 15:35:04,372 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第5/7天
2025-07-06 15:35:04,373 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-22
2025-07-06 15:35:04,373 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第6/7天
2025-07-06 15:35:04,373 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-23
2025-07-06 15:35:04,374 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第7/7天
2025-07-06 15:35:04,374 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 15:35:04,374 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 15:35:04,375 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 15:35:04,375 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 15:35:04,376 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 15:35:04,376 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 15:35:04,376 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 15:35:04,377 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 15:35:04,377 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 15:35:04,377 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 15:35:04,377 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-06 15:35:04,377 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 15:35:04,378 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-06 15:35:04,378 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 15:35:04,378 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-06 15:35:04,378 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 15:35:04,378 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-06 15:35:04,378 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 15:35:04,379 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-06 15:35:04,379 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 15:35:04,379 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-06 15:35:04,379 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 15:35:04,380 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-06 15:35:04,380 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-06 15:35:04,380 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-06 15:35:04,381 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 15:35:04,381 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['NAA', 'TAA']
2025-07-06 15:35:04,381 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['NAA', 'TAA']
2025-07-06 15:35:04,381 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['NAA', 'TAA']
2025-07-06 15:35:04,381 - contribution_assessment.weekly_opro_manager - INFO - 开始每周优化循环 - 周期: 2025-01-17
2025-07-06 15:35:04,382 - contribution_assessment.weekly_opro_manager - ERROR - 代理 NAA 优化失败: 'dict' object has no attribute 'optimize_agent_prompt'
2025-07-06 15:35:04,382 - contribution_assessment.weekly_opro_manager - ERROR - 代理 TAA 优化失败: 'dict' object has no attribute 'optimize_agent_prompt'
2025-07-06 15:35:04,400 - contribution_assessment.weekly_opro_manager - INFO - 每周优化循环完成 - 周期: 2025-01-17
2025-07-06 15:35:04,400 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 15:35:04,400 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 15:35:04,400 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-24
2025-07-06 15:35:04,401 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-27
2025-07-06 15:35:04,401 - __main__ - INFO - 🆕 开始第4周 - A/B测试阶段: 2025-01-27
2025-07-06 15:35:04,401 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第1/7天
2025-07-06 15:35:04,401 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-28
2025-07-06 15:35:04,401 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第2/7天
2025-07-06 15:35:04,401 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-29
2025-07-06 15:35:04,401 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第3/7天
2025-07-06 15:35:04,401 - __main__ - INFO - ✅ 第 3 周完成: baseline_complete
2025-07-06 15:35:04,401 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 15:35:04,402 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 15:35:04,402 - __main__ - INFO -   - 总周期数: 3
2025-07-06 15:35:04,402 - __main__ - INFO -   - 成功周期: 2
2025-07-06 15:35:04,402 - __main__ - INFO -   - 失败周期: 1
2025-07-06 15:35:04,403 - __main__ - INFO -   - 成功率: 66.67%
2025-07-06 15:35:04,403 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 15:35:04,403 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,404 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 15:35:04,404 - __main__ - INFO - 📊 总周期数: 3
2025-07-06 15:35:04,404 - __main__ - INFO - 📊 总交易天数: 23
2025-07-06 15:35:04,404 - __main__ - INFO - ⏱️  总执行时间: 0.09秒
2025-07-06 15:35:04,405 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,405 - __main__ - INFO - 开始交易会话: assessment_20250706_153504
2025-07-06 15:35:04,405 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 15:35:04,416 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_153504
2025-07-06 15:35:04,416 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_153504 (系统盈亏: 0.00)
2025-07-06 15:35:04,416 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_153504
2025-07-06 15:35:04,416 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 15:35:04,416 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 15:35:04,416 - __main__ - INFO - ====================================================================================================
2025-07-06 15:35:04,417 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 15:35:04,417 - __main__ - INFO - ====================================================================================================
