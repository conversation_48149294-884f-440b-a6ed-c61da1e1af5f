[{"timestamp": "2025-07-06T14:08:09.661568", "output_id": "output_20250706_140809_1d15449e", "input_id": "input_20250706_140805_5e0899b7", "prompt_id": "prompt_20250706_140805_0a53c0cb", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 60, "analysis": "RSI is above 50, suggesting a neutral trend."}, "MACD": {"value": "0.01", "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, which suggests a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:09.657557", "processing_time": 4.598662, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 60, "analysis": "RSI is above 50, suggesting a neutral trend."}, "MACD": {"value": "0.01", "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, which suggests a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:09.657557", "processing_time": 4.598662, "llm_used": true}, "processing_time": 4.598662, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 10.242757000000001}}, {"timestamp": "2025-07-06T14:08:10.001444", "output_id": "output_20250706_140810_8a951a56", "input_id": "input_20250706_140805_9b6fd1ae", "prompt_id": "prompt_20250706_140806_9940acf5", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.001444", "processing_time": 4.766939, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.001444", "processing_time": 4.766939, "llm_used": true}, "processing_time": 4.766939, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.009696000000002}}, {"timestamp": "2025-07-06T14:08:10.031603", "output_id": "output_20250706_140810_25d8c7e0", "input_id": "input_20250706_140805_4a2edec7", "prompt_id": "prompt_20250706_140806_b80b9cf3", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.025096", "processing_time": 4.700671, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.025096", "processing_time": 4.700671, "llm_used": true}, "processing_time": 4.700671, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 19.710367}}, {"timestamp": "2025-07-06T14:08:10.040195", "output_id": "output_20250706_140810_58cb121a", "input_id": "input_20250706_140805_1cd40c64", "prompt_id": "prompt_20250706_140806_d046c685", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.040195", "processing_time": 4.716296, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.040195", "processing_time": 4.716296, "llm_used": true}, "processing_time": 4.716296, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 24.426663}}, {"timestamp": "2025-07-06T14:08:10.536213", "output_id": "output_20250706_140810_5564780a", "input_id": "input_20250706_140805_a433b618", "prompt_id": "prompt_20250706_140806_f9b06d29", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.536213", "processing_time": 5.318672, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.536213", "processing_time": 5.318672, "llm_used": true}, "processing_time": 5.318672, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 29.745335}}, {"timestamp": "2025-07-06T14:08:10.811624", "output_id": "output_20250706_140810_e954adaf", "input_id": "input_20250706_140805_d03ad0da", "prompt_id": "prompt_20250706_140806_9bc91a2a", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the asset may be overbought, but the overall trend is bullish."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.811624", "processing_time": 5.644474, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the asset may be overbought, but the overall trend is bullish."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.811624", "processing_time": 5.644474, "llm_used": true}, "processing_time": 5.644474, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 35.389809}}, {"timestamp": "2025-07-06T14:08:11.220005", "output_id": "output_20250706_140811_24d61926", "input_id": "input_20250706_140805_1c1b7df4", "prompt_id": "prompt_20250706_140805_5c7bbe80", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions."}, "MACD": {"signal_line": 0, "histogram": {"current_value": 0.5, "trend": "positive"}, "analysis": "MACD histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"short_term_MA": 160.0, "long_term_MA": 150.0, "analysis": "The stock is trading above both the short-term and long-term moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.220005", "processing_time": 6.092384, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions."}, "MACD": {"signal_line": 0, "histogram": {"current_value": 0.5, "trend": "positive"}, "analysis": "MACD histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"short_term_MA": 160.0, "long_term_MA": 150.0, "analysis": "The stock is trading above both the short-term and long-term moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.220005", "processing_time": 6.092384, "llm_used": true}, "processing_time": 6.092384, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 41.482193}}, {"timestamp": "2025-07-06T14:08:11.240179", "output_id": "output_20250706_140811_2a373d1e", "input_id": "input_20250706_140805_8487a794", "prompt_id": "prompt_20250706_140806_a0971af2", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.240179", "processing_time": 5.918377, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.240179", "processing_time": 5.918377, "llm_used": true}, "processing_time": 5.918377, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 47.40057}}, {"timestamp": "2025-07-06T14:08:11.385276", "output_id": "output_20250706_140811_32b40a3b", "input_id": "input_20250706_140805_fac9e55f", "prompt_id": "prompt_20250706_140806_fa7a3cd3", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Both the signal line and histogram are near zero, suggesting no clear trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, but both are relatively stable, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.385276", "processing_time": 6.061377, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Both the signal line and histogram are near zero, suggesting no clear trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, but both are relatively stable, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.385276", "processing_time": 6.061377, "llm_used": true}, "processing_time": 6.061377, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 53.461947}}, {"timestamp": "2025-07-06T14:08:12.135271", "output_id": "output_20250706_140812_bc28a881", "input_id": "input_20250706_140808_ecdd211d", "prompt_id": "prompt_20250706_140808_28141570", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.135271", "processing_time": 4.04116, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.135271", "processing_time": 4.04116, "llm_used": true}, "processing_time": 4.04116, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 57.503107}}, {"timestamp": "2025-07-06T14:08:12.824648", "output_id": "output_20250706_140812_bc5c5afe", "input_id": "input_20250706_140808_388cd0ed", "prompt_id": "prompt_20250706_140808_7ee33638", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 60, indicating that the stock may be overbought but still in a strong bullish trend."}, "MACD": {"current_value": 0.05, "interpretation": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.824648", "processing_time": 4.350726, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 60, indicating that the stock may be overbought but still in a strong bullish trend."}, "MACD": {"current_value": 0.05, "interpretation": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.824648", "processing_time": 4.350726, "llm_used": true}, "processing_time": 4.350726, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 61.853833}}, {"timestamp": "2025-07-06T14:08:13.041646", "output_id": "output_20250706_140813_80b81bb7", "input_id": "input_20250706_140808_c159f0da", "prompt_id": "prompt_20250706_140809_61166ec8", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.2, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.041646", "processing_time": 4.049482, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.2, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.041646", "processing_time": 4.049482, "llm_used": true}, "processing_time": 4.049482, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 65.903315}}, {"timestamp": "2025-07-06T14:08:13.082533", "output_id": "output_20250706_140813_cdebe0ef", "input_id": "input_20250706_140809_a2febe0b", "prompt_id": "prompt_20250706_140809_ae5402b2", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"bullish": true, "cross": "above"}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.082533", "processing_time": 3.992888, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"bullish": true, "cross": "above"}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.082533", "processing_time": 3.992888, "llm_used": true}, "processing_time": 3.992888, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 69.896203}}, {"timestamp": "2025-07-06T14:08:13.358196", "output_id": "output_20250706_140813_0481b7de", "input_id": "input_20250706_140809_db8dd30e", "prompt_id": "prompt_20250706_140809_6a2380c0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "bullish_cross": true}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.358196", "processing_time": 3.875374, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "bullish_cross": true}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.358196", "processing_time": 3.875374, "llm_used": true}, "processing_time": 3.875374, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 73.771577}}, {"timestamp": "2025-07-06T14:08:14.962610", "output_id": "output_20250706_140814_befebacf", "input_id": "input_20250706_140809_79a3756a", "prompt_id": "prompt_20250706_140809_725ffd31", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating that the stock may be overbought, but the upward trend is strong."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD signal line is above the zero line and the histogram is rising, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:14.962610", "processing_time": 5.258142, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating that the stock may be overbought, but the upward trend is strong."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD signal line is above the zero line and the histogram is rising, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:14.962610", "processing_time": 5.258142, "llm_used": true}, "processing_time": 5.258142, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 79.029719}}, {"timestamp": "2025-07-06T14:08:15.228722", "output_id": "output_20250706_140815_24cef2c3", "input_id": "input_20250706_140809_9e7c2ed7", "prompt_id": "prompt_20250706_140809_28ae4eab", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating that the stock might be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 20, "histogram": {"current_value": 0.5, "analysis": "MACD histogram is positive and rising, suggesting a strong bullish trend."}}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:15.228722", "processing_time": 5.600479, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating that the stock might be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 20, "histogram": {"current_value": 0.5, "analysis": "MACD histogram is positive and rising, suggesting a strong bullish trend."}}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:15.228722", "processing_time": 5.600479, "llm_used": true}, "processing_time": 5.600479, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 84.63019800000001}}, {"timestamp": "2025-07-06T14:08:17.389049", "output_id": "output_20250706_140817_50fc906f", "input_id": "input_20250706_140809_b2bb08d2", "prompt_id": "prompt_20250706_140809_46be984b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above long-term MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:17.380203", "processing_time": 8.36351, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above long-term MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:17.380203", "processing_time": 8.36351, "llm_used": true}, "processing_time": 8.36351, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 92.99370800000001}}, {"timestamp": "2025-07-06T14:08:23.187462", "output_id": "output_20250706_140823_87bc339f", "input_id": "input_20250706_140818_f046689a", "prompt_id": "prompt_20250706_140818_2d5767ac", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 60, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "comment": "MACD signal line is above the zero line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:23.187462", "processing_time": 4.8535, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 60, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "comment": "MACD signal line is above the zero line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:23.187462", "processing_time": 4.8535, "llm_used": true}, "processing_time": 4.8535, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 97.84720800000001}}, {"timestamp": "2025-07-06T14:08:28.441753", "output_id": "output_20250706_140828_0e39b6f0", "input_id": "input_20250706_140823_6d98d34a", "prompt_id": "prompt_20250706_140823_7bd76526", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:28.399612", "processing_time": 4.511874, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:28.399612", "processing_time": 4.511874, "llm_used": true}, "processing_time": 4.511874, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 102.35908200000002}}, {"timestamp": "2025-07-06T14:08:29.970238", "output_id": "output_20250706_140829_8e356d6b", "input_id": "input_20250706_140825_170226ae", "prompt_id": "prompt_20250706_140825_2d189bab", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating that the stock may be overbought, but the upbeat news could justify this."}, "MACD": {"current_value": "positive crossover", "interpretation": "MACD is showing a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "interpretation": "The stock is above both the 50-Day and 200-Day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:29.951706", "processing_time": 4.594958, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating that the stock may be overbought, but the upbeat news could justify this."}, "MACD": {"current_value": "positive crossover", "interpretation": "MACD is showing a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "interpretation": "The stock is above both the 50-Day and 200-Day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:29.951706", "processing_time": 4.594958, "llm_used": true}, "processing_time": 4.594958, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 106.95404000000002}}, {"timestamp": "2025-07-06T14:08:33.062774", "output_id": "output_20250706_140833_56b82d5a", "input_id": "input_20250706_140827_15262625", "prompt_id": "prompt_20250706_140827_f164db3f", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.005591", "processing_time": 5.172127, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.005591", "processing_time": 5.172127, "llm_used": true}, "processing_time": 5.172127, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 112.12616700000002}}, {"timestamp": "2025-07-06T14:08:33.134693", "output_id": "output_20250706_140833_b6fe7f3d", "input_id": "input_20250706_140829_340ecca7", "prompt_id": "prompt_20250706_140829_ba6535aa", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.110913", "processing_time": 3.919328, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.110913", "processing_time": 3.919328, "llm_used": true}, "processing_time": 3.919328, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 116.04549500000002}}, {"timestamp": "2025-07-06T14:08:33.285737", "output_id": "output_20250706_140833_159b7a02", "input_id": "input_20250706_140828_08afc949", "prompt_id": "prompt_20250706_140828_b196f69c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0, "histogram": 0.1, "comment": "MACD line is above the signal line, suggesting a bullish momentum."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 140, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.233643", "processing_time": 4.731884, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0, "histogram": 0.1, "comment": "MACD line is above the signal line, suggesting a bullish momentum."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 140, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.233643", "processing_time": 4.731884, "llm_used": true}, "processing_time": 4.731884, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 120.77737900000001}}, {"timestamp": "2025-07-06T14:08:35.650234", "output_id": "output_20250706_140835_5c449246", "input_id": "input_20250706_140831_c1274f4c", "prompt_id": "prompt_20250706_140831_e04ae117", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:35.650234", "processing_time": 4.206354, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:35.650234", "processing_time": 4.206354, "llm_used": true}, "processing_time": 4.206354, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 124.98373300000002}}, {"timestamp": "2025-07-06T14:08:36.397223", "output_id": "output_20250706_140836_20d48e14", "input_id": "input_20250706_140831_e6d6ec55", "prompt_id": "prompt_20250706_140831_85ae6716", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "positive crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:36.397223", "processing_time": 5.146734, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "positive crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:36.397223", "processing_time": 5.146734, "llm_used": true}, "processing_time": 5.146734, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 130.130467}}, {"timestamp": "2025-07-06T14:08:37.840005", "output_id": "output_20250706_140837_b96a51f4", "input_id": "input_20250706_140832_4810d790", "prompt_id": "prompt_20250706_140833_2458ed9c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 160.0, "200_day": 180.0, "signal": "price above 50 and 200 day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:37.826521", "processing_time": 4.883599, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 160.0, "200_day": 180.0, "signal": "price above 50 and 200 day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:37.826521", "processing_time": 4.883599, "llm_used": true}, "processing_time": 4.883599, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 135.014066}}, {"timestamp": "2025-07-06T14:08:38.017072", "output_id": "output_20250706_140838_3938f486", "input_id": "input_20250706_140832_6e384a5b", "prompt_id": "prompt_20250706_140832_82407442", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "above MA50 and MA200"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:38.017072", "processing_time": 5.123621, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "above MA50 and MA200"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:38.017072", "processing_time": 5.123621, "llm_used": true}, "processing_time": 5.123621, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 140.13768700000003}}, {"timestamp": "2025-07-06T14:08:40.078485", "output_id": "output_20250706_140840_ed2d29d1", "input_id": "input_20250706_140835_2306f65d", "prompt_id": "prompt_20250706_140835_4aadeb60", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.078485", "processing_time": 4.370178, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.078485", "processing_time": 4.370178, "llm_used": true}, "processing_time": 4.370178, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 144.50786500000004}}, {"timestamp": "2025-07-06T14:08:40.272414", "output_id": "output_20250706_140840_8c51cb93", "input_id": "input_20250706_140835_4a97a5d1", "prompt_id": "prompt_20250706_140835_c2a506f8", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "Overbought, indicating a possible pullback or short-term reversal."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover and histogram above zero, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.272414", "processing_time": 4.33821, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "Overbought, indicating a possible pullback or short-term reversal."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover and histogram above zero, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.272414", "processing_time": 4.33821, "llm_used": true}, "processing_time": 4.33821, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 148.84607500000004}}, {"timestamp": "2025-07-06T14:08:41.740226", "output_id": "output_20250706_140841_f9467a1c", "input_id": "input_20250706_140837_44c53a62", "prompt_id": "prompt_20250706_140837_41418d33", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.711630", "processing_time": 4.164623, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.711630", "processing_time": 4.164623, "llm_used": true}, "processing_time": 4.164623, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 153.01069800000005}}, {"timestamp": "2025-07-06T14:08:41.813797", "output_id": "output_20250706_140841_c55144b4", "input_id": "input_20250706_140837_545f5b1c", "prompt_id": "prompt_20250706_140837_7e4f9101", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI接近70，表明股票可能处于超买状态，但短期内可能仍有上升空间。"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "comment": "MACD的信号线和柱状图均显示多头信号，表明市场趋势可能继续上涨。"}, "MA": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "50日移动平均线位于200日移动平均线之上，表明长期趋势向上。"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.813797", "processing_time": 4.762145, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI接近70，表明股票可能处于超买状态，但短期内可能仍有上升空间。"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "comment": "MACD的信号线和柱状图均显示多头信号，表明市场趋势可能继续上涨。"}, "MA": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "50日移动平均线位于200日移动平均线之上，表明长期趋势向上。"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.813797", "processing_time": 4.762145, "llm_used": true}, "processing_time": 4.762145, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 157.77284300000005}}, {"timestamp": "2025-07-06T14:08:44.096163", "output_id": "output_20250706_140844_75872f78", "input_id": "input_20250706_140836_c245d867", "prompt_id": "prompt_20250706_140836_f8870468", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53.6, "signal": "neutral"}, "MACD": {"signal_line": 0.2, "histogram": -0.3, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:44.096163", "processing_time": 8.061078, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53.6, "signal": "neutral"}, "MACD": {"signal_line": 0.2, "histogram": -0.3, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:44.096163", "processing_time": 8.061078, "llm_used": true}, "processing_time": 8.061078, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 165.83392100000006}}, {"timestamp": "2025-07-06T14:09:21.174289", "output_id": "output_20250706_140921_a2210ee7", "input_id": "input_20250706_140916_4a3c29ba", "prompt_id": "prompt_20250706_140916_fa458774", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD is positive and above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:09:21.174289", "processing_time": 4.598427, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD is positive and above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:09:21.174289", "processing_time": 4.598427, "llm_used": true}, "processing_time": 4.598427, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 4.598427}}, {"timestamp": "2025-07-06T14:10:01.811911", "output_id": "output_20250706_141001_f6a01cad", "input_id": "input_20250706_140956_9ed7e477", "prompt_id": "prompt_20250706_140957_0b0fd428", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "historical_line": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:01.811911", "processing_time": 5.345959, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "historical_line": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:01.811911", "processing_time": 5.345959, "llm_used": true}, "processing_time": 5.345959, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 9.944386}}, {"timestamp": "2025-07-06T14:10:02.340584", "output_id": "output_20250706_141002_de53e91f", "input_id": "input_20250706_140956_835fee25", "prompt_id": "prompt_20250706_140958_6d788b45", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:02.296579", "processing_time": 5.746941, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:02.296579", "processing_time": 5.746941, "llm_used": true}, "processing_time": 5.746941, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.691327}}, {"timestamp": "2025-07-06T14:10:03.235016", "output_id": "output_20250706_141003_7b105031", "input_id": "input_20250706_140957_0366e59f", "prompt_id": "prompt_20250706_140958_8ff46ec6", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.235016", "processing_time": 6.587497, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.235016", "processing_time": 6.587497, "llm_used": true}, "processing_time": 6.587497, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 22.278824}}, {"timestamp": "2025-07-06T14:10:03.426267", "output_id": "output_20250706_141003_ad76c283", "input_id": "input_20250706_140957_d2685554", "prompt_id": "prompt_20250706_140958_941c06c9", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is in overbought territory, indicating potential for a pullback."}, "MACD": {"signal_line": 0.02, "comment": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "comment": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.426267", "processing_time": 6.663319, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is in overbought territory, indicating potential for a pullback."}, "MACD": {"signal_line": 0.02, "comment": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "comment": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.426267", "processing_time": 6.663319, "llm_used": true}, "processing_time": 6.663319, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 28.942143}}, {"timestamp": "2025-07-06T14:10:04.219779", "output_id": "output_20250706_141004_71d8b18a", "input_id": "input_20250706_140957_4b53fd1f", "prompt_id": "prompt_20250706_140958_d496718e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock is overbought but still in a strong bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.219779", "processing_time": 7.476308, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock is overbought but still in a strong bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.219779", "processing_time": 7.476308, "llm_used": true}, "processing_time": 7.476308, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 36.418451000000005}}, {"timestamp": "2025-07-06T14:10:04.806925", "output_id": "output_20250706_141004_017a2104", "input_id": "input_20250706_140959_501219dd", "prompt_id": "prompt_20250706_140959_8449926d", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions. However, the upward trend suggests that the overbought state is temporary."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "analysis": "The MACD line is above the signal line, and the histogram is positive, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.770758", "processing_time": 5.421368, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions. However, the upward trend suggests that the overbought state is temporary."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "analysis": "The MACD line is above the signal line, and the histogram is positive, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.770758", "processing_time": 5.421368, "llm_used": true}, "processing_time": 5.421368, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 41.839819000000006}}, {"timestamp": "2025-07-06T14:10:04.927700", "output_id": "output_20250706_141004_88964087", "input_id": "input_20250706_140957_fd2e3e20", "prompt_id": "prompt_20250706_140958_90d486ba", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.927700", "processing_time": 8.114565, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.927700", "processing_time": 8.114565, "llm_used": true}, "processing_time": 8.114565, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 49.954384000000005}}, {"timestamp": "2025-07-06T14:10:05.233594", "output_id": "output_20250706_141005_032cea1b", "input_id": "input_20250706_141001_b1fbbdbe", "prompt_id": "prompt_20250706_141001_d8578859", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.233594", "processing_time": 3.633697, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.233594", "processing_time": 3.633697, "llm_used": true}, "processing_time": 3.633697, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 53.588081}}, {"timestamp": "2025-07-06T14:10:05.628365", "output_id": "output_20250706_141005_76554bd4", "input_id": "input_20250706_140957_5675278e", "prompt_id": "prompt_20250706_140958_a4fa2c3b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 70.5, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above long-term MA, but close to short-term MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.628365", "processing_time": 8.877897, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 70.5, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above long-term MA, but close to short-term MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.628365", "processing_time": 8.877897, "llm_used": true}, "processing_time": 8.877897, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 62.46597800000001}}, {"timestamp": "2025-07-06T14:10:05.713682", "output_id": "output_20250706_141005_cbfbf274", "input_id": "input_20250706_140957_9c7084a2", "prompt_id": "prompt_20250706_140958_306183f5", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 50 indicating a strong uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "comment": "MACD line above the signal line with a rising histogram indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "Stock price above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.669194", "processing_time": 9.072194, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 50 indicating a strong uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "comment": "MACD line above the signal line with a rising histogram indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "Stock price above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.669194", "processing_time": 9.072194, "llm_used": true}, "processing_time": 9.072194, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 71.538172}}, {"timestamp": "2025-07-06T14:10:05.831647", "output_id": "output_20250706_141005_f13579e1", "input_id": "input_20250706_141001_36c9922f", "prompt_id": "prompt_20250706_141001_fb6d9960", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD is above the signal line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.831647", "processing_time": 4.596848, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD is above the signal line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.831647", "processing_time": 4.596848, "llm_used": true}, "processing_time": 4.596848, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 76.13502}}, {"timestamp": "2025-07-06T14:10:06.746388", "output_id": "output_20250706_141006_45e1c6a2", "input_id": "input_20250706_141002_59c722db", "prompt_id": "prompt_20250706_141002_f1922c4e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 0.5, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.746388", "processing_time": 4.59609, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 0.5, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.746388", "processing_time": 4.59609, "llm_used": true}, "processing_time": 4.59609, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 80.73111}}, {"timestamp": "2025-07-06T14:10:06.816267", "output_id": "output_20250706_141006_5f12d638", "input_id": "input_20250706_141001_19676062", "prompt_id": "prompt_20250706_141001_5864fbd0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.815268", "processing_time": 5.434962, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.815268", "processing_time": 5.434962, "llm_used": true}, "processing_time": 5.434962, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 86.166072}}, {"timestamp": "2025-07-06T14:10:07.330324", "output_id": "output_20250706_141007_2d36512a", "input_id": "input_20250706_141002_52d758bc", "prompt_id": "prompt_20250706_141002_6b469377", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.217544", "processing_time": 4.876409, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.217544", "processing_time": 4.876409, "llm_used": true}, "processing_time": 4.876409, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 91.042481}}, {"timestamp": "2025-07-06T14:10:07.336852", "output_id": "output_20250706_141007_d3207606", "input_id": "input_20250706_141002_16a9bc2b", "prompt_id": "prompt_20250706_141002_0e0440ce", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68.5, "comment": "RSI is above 70, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The 50-day moving average is above the 200-day moving average, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.258233", "processing_time": 5.19206, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68.5, "comment": "RSI is above 70, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The 50-day moving average is above the 200-day moving average, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.258233", "processing_time": 5.19206, "llm_used": true}, "processing_time": 5.19206, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 96.234541}}, {"timestamp": "2025-07-06T14:10:08.430558", "output_id": "output_20250706_141008_8180a48e", "input_id": "input_20250706_140957_c56e1557", "prompt_id": "prompt_20250706_140958_f8a183e3", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:08.429550", "processing_time": 11.613408, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:08.429550", "processing_time": 11.613408, "llm_used": true}, "processing_time": 11.613408, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 107.847949}}, {"timestamp": "2025-07-06T14:10:09.213453", "output_id": "output_20250706_141009_fe898a87", "input_id": "input_20250706_141001_e1d5324e", "prompt_id": "prompt_20250706_141001_ea2b878d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"current_value": 0.03, "trend": "positive"}}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 120.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:09.213453", "processing_time": 7.851076, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"current_value": 0.03, "trend": "positive"}}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 120.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:09.213453", "processing_time": 7.851076, "llm_used": true}, "processing_time": 7.851076, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 115.699025}}, {"timestamp": "2025-07-06T14:10:13.446427", "output_id": "output_20250706_141013_c5204ccc", "input_id": "input_20250706_141009_f6b6ce30", "prompt_id": "prompt_20250706_141009_f9901bae", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:13.366924", "processing_time": 4.060302, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:13.366924", "processing_time": 4.060302, "llm_used": true}, "processing_time": 4.060302, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 119.75932700000001}}, {"timestamp": "2025-07-06T14:10:22.009218", "output_id": "output_20250706_141022_5feae71b", "input_id": "input_20250706_141018_92bfc317", "prompt_id": "prompt_20250706_141018_053f4fa4", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69.2, "overbought": true, "signal": "buy"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "signal": "buy"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "buy"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.004235", "processing_time": 3.704471, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69.2, "overbought": true, "signal": "buy"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "signal": "buy"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "buy"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.004235", "processing_time": 3.704471, "llm_used": true}, "processing_time": 3.704471, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 123.46379800000001}}, {"timestamp": "2025-07-06T14:10:22.786515", "output_id": "output_20250706_141022_0e327ec5", "input_id": "input_20250706_141018_1c7266a5", "prompt_id": "prompt_20250706_141018_8df95c86", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the range, suggesting no immediate overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "MACD line is close to the signal line, indicating a lack of momentum in either direction."}, "Moving_Average": {"short_term_MA": 155.0, "long_term_MA": 150.0, "analysis": "The stock is currently trading above the short-term moving average but below the long-term moving average, suggesting a slight bullish trend in the short term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.786515", "processing_time": 4.737297, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the range, suggesting no immediate overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "MACD line is close to the signal line, indicating a lack of momentum in either direction."}, "Moving_Average": {"short_term_MA": 155.0, "long_term_MA": 150.0, "analysis": "The stock is currently trading above the short-term moving average but below the long-term moving average, suggesting a slight bullish trend in the short term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.786515", "processing_time": 4.737297, "llm_used": true}, "processing_time": 4.737297, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 128.201095}}, {"timestamp": "2025-07-06T14:10:23.912796", "output_id": "output_20250706_141023_68a59545", "input_id": "input_20250706_141019_f4471fe4", "prompt_id": "prompt_20250706_141019_f0019687", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140, "200_day_MA": 130, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:23.912796", "processing_time": 4.587495, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140, "200_day_MA": 130, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:23.912796", "processing_time": 4.587495, "llm_used": true}, "processing_time": 4.587495, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 132.78859}}, {"timestamp": "2025-07-06T14:10:27.695772", "output_id": "output_20250706_141027_e393ad45", "input_id": "input_20250706_141021_f15492d8", "prompt_id": "prompt_20250706_141021_1b25311f", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the trend is still bullish."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:27.695772", "processing_time": 6.548071, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the trend is still bullish."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:27.695772", "processing_time": 6.548071, "llm_used": true}, "processing_time": 6.548071, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 139.336661}}, {"timestamp": "2025-07-06T14:10:29.378697", "output_id": "output_20250706_141029_782dc78e", "input_id": "input_20250706_141024_a9df1325", "prompt_id": "prompt_20250706_141024_a194e9f4", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "Buy signal"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Short-term trend above long-term trend, indicating bullishness"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.378697", "processing_time": 5.154446, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "Buy signal"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Short-term trend above long-term trend, indicating bullishness"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.378697", "processing_time": 5.154446, "llm_used": true}, "processing_time": 5.154446, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 144.491107}}, {"timestamp": "2025-07-06T14:10:29.765589", "output_id": "output_20250706_141029_0b36f9d5", "input_id": "input_20250706_141023_d0c39578", "prompt_id": "prompt_20250706_141023_98a66153", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50-Day_MA": 150.0, "200-Day_MA": 130.0, "signal": "crossover above 50-Day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.765589", "processing_time": 6.702852, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50-Day_MA": 150.0, "200-Day_MA": 130.0, "signal": "crossover above 50-Day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.765589", "processing_time": 6.702852, "llm_used": true}, "processing_time": 6.702852, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 151.193959}}, {"timestamp": "2025-07-06T14:10:30.966018", "output_id": "output_20250706_141030_1046a242", "input_id": "input_20250706_141024_62c8edb1", "prompt_id": "prompt_20250706_141024_8e6acedd", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:30.832683", "processing_time": 6.740898, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:30.832683", "processing_time": 6.740898, "llm_used": true}, "processing_time": 6.740898, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 157.934857}}, {"timestamp": "2025-07-06T14:10:31.871895", "output_id": "output_20250706_141031_678cd651", "input_id": "input_20250706_141027_4a378eca", "prompt_id": "prompt_20250706_141027_9a2ac0ee", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "trend": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:31.871895", "processing_time": 4.622116, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "trend": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:31.871895", "processing_time": 4.622116, "llm_used": true}, "processing_time": 4.622116, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 162.556973}}, {"timestamp": "2025-07-06T14:10:33.179188", "output_id": "output_20250706_141033_d46c15d2", "input_id": "input_20250706_141029_c0867dda", "prompt_id": "prompt_20250706_141029_7b399b7c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is overbought, indicating potential for a pullback."}, "MACD": {"signal_line": 0, "analysis": "MACD is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:33.179188", "processing_time": 4.061227, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is overbought, indicating potential for a pullback."}, "MACD": {"signal_line": 0, "analysis": "MACD is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:33.179188", "processing_time": 4.061227, "llm_used": true}, "processing_time": 4.061227, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 166.6182}}, {"timestamp": "2025-07-06T14:10:35.100694", "output_id": "output_20250706_141035_b473f046", "input_id": "input_20250706_141030_bb82033b", "prompt_id": "prompt_20250706_141030_c768b64c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.033993", "processing_time": 4.881211, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.033993", "processing_time": 4.881211, "llm_used": true}, "processing_time": 4.881211, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 171.499411}}, {"timestamp": "2025-07-06T14:10:35.439895", "output_id": "output_20250706_141035_8cac1004", "input_id": "input_20250706_141030_53d485e3", "prompt_id": "prompt_20250706_141030_6a0a05b2", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.439895", "processing_time": 4.692803, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.439895", "processing_time": 4.692803, "llm_used": true}, "processing_time": 4.692803, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 176.192214}}, {"timestamp": "2025-07-06T14:10:35.545415", "output_id": "output_20250706_141035_d47469ff", "input_id": "input_20250706_141030_8e34d20b", "prompt_id": "prompt_20250706_141030_3a563ea9", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.545415", "processing_time": 5.161815, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.545415", "processing_time": 5.161815, "llm_used": true}, "processing_time": 5.161815, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 181.354029}}, {"timestamp": "2025-07-06T14:10:35.616774", "output_id": "output_20250706_141035_ff56f1f8", "input_id": "input_20250706_141029_e22dd694", "prompt_id": "prompt_20250706_141029_26b37c0b", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating the stock is overbought, but the upbeat news might push it higher."}, "MACD": {"signal_line": 15, "histogram": 5, "analysis": "MACD is showing a bullish crossover, suggesting potential upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is currently above both the 50-Day and 200-Day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.585941", "processing_time": 6.378365, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating the stock is overbought, but the upbeat news might push it higher."}, "MACD": {"signal_line": 15, "histogram": 5, "analysis": "MACD is showing a bullish crossover, suggesting potential upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is currently above both the 50-Day and 200-Day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.585941", "processing_time": 6.378365, "llm_used": true}, "processing_time": 6.378365, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 187.732394}}, {"timestamp": "2025-07-06T14:10:35.761509", "output_id": "output_20250706_141035_615ecaed", "input_id": "input_20250706_141031_21c6fb3d", "prompt_id": "prompt_20250706_141031_fc915115", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.761509", "processing_time": 4.461422, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.761509", "processing_time": 4.461422, "llm_used": true}, "processing_time": 4.461422, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 192.193816}}, {"timestamp": "2025-07-06T15:10:08.072223", "output_id": "output_20250706_151008_5f444b06", "input_id": "input_20250706_151003_9ec97cfc", "prompt_id": "prompt_20250706_151003_2dc194d1", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "MACD line above signal line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock trading above both 50-day and 200-day moving averages, indicating strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:08.072223", "processing_time": 4.790774, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "MACD line above signal line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock trading above both 50-day and 200-day moving averages, indicating strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:08.072223", "processing_time": 4.790774, "llm_used": true}, "processing_time": 4.790774, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 12.135632999999999}}, {"timestamp": "2025-07-06T15:10:09.422308", "output_id": "output_20250706_151009_fead1eda", "input_id": "input_20250706_151003_f6afb9d1", "prompt_id": "prompt_20250706_151004_08230771", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "Neutral: RSI is currently in the middle of the overbought/oversold zone."}, "MACD": {"current_value": "-0.1", "analysis": "Neutral: The MACD line is close to the signal line, indicating a lack of strong trend."}, "Moving_Average": {"simple_moving_average": {"50_day": 150.0, "200_day": 160.0, "analysis": "Neutral: The 50-day moving average is close to the current price, while the 200-day moving average is above, suggesting a sideways trend."}}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:09.266979", "processing_time": 5.822364, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "Neutral: RSI is currently in the middle of the overbought/oversold zone."}, "MACD": {"current_value": "-0.1", "analysis": "Neutral: The MACD line is close to the signal line, indicating a lack of strong trend."}, "Moving_Average": {"simple_moving_average": {"50_day": 150.0, "200_day": 160.0, "analysis": "Neutral: The 50-day moving average is close to the current price, while the 200-day moving average is above, suggesting a sideways trend."}}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:09.266979", "processing_time": 5.822364, "llm_used": true}, "processing_time": 5.822364, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 17.957997}}, {"timestamp": "2025-07-06T15:10:10.250559", "output_id": "output_20250706_151010_1b5b3c6e", "input_id": "input_20250706_151004_f9dada80", "prompt_id": "prompt_20250706_151005_b69936e1", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.2, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": {"type": "no_cross", "timeframe": "short_term"}}, "Moving_Average": {"50_day": {"current_value": 152.5, "trend": "neutral"}, "200_day": {"current_value": 145.0, "trend": "neutral"}}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:10.190256", "processing_time": 6.244244, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.2, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": {"type": "no_cross", "timeframe": "short_term"}}, "Moving_Average": {"50_day": {"current_value": 152.5, "trend": "neutral"}, "200_day": {"current_value": 145.0, "trend": "neutral"}}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:10.190256", "processing_time": 6.244244, "llm_used": true}, "processing_time": 6.244244, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 24.202241}}, {"timestamp": "2025-07-06T15:10:10.558051", "output_id": "output_20250706_151010_f1c9a1fc", "input_id": "input_20250706_151004_0e082fe1", "prompt_id": "prompt_20250706_151005_6c22552b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "overbought": false}, "MACD": {"signal_line": 0.0, "histogram": {"current_value": 0.1, "positive": true}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "50_day_MA above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:10.558051", "processing_time": 6.704135, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "overbought": false}, "MACD": {"signal_line": 0.0, "histogram": {"current_value": 0.1, "positive": true}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "50_day_MA above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:10.558051", "processing_time": 6.704135, "llm_used": true}, "processing_time": 6.704135, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 30.906376}}, {"timestamp": "2025-07-06T15:10:11.163727", "output_id": "output_20250706_151011_fd9f78c2", "input_id": "input_20250706_151004_684c5f77", "prompt_id": "prompt_20250706_151005_68019965", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "MACD signal line positive and rising, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "Stock trading above both 50-day and 200-day moving averages, strong bullish signal"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:11.163727", "processing_time": 7.351285, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "MACD signal line positive and rising, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "Stock trading above both 50-day and 200-day moving averages, strong bullish signal"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:11.163727", "processing_time": 7.351285, "llm_used": true}, "processing_time": 7.351285, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 38.257661}}, {"timestamp": "2025-07-06T15:10:11.645880", "output_id": "output_20250706_151011_17a032b4", "input_id": "input_20250706_151004_202a05bb", "prompt_id": "prompt_20250706_151005_f41980a1", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating that the stock is overbought and might be due for a pullback."}, "MACD": {"current_value": "positive", "analysis": "MACD is in a positive position, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:11.645880", "processing_time": 7.698857, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating that the stock is overbought and might be due for a pullback."}, "MACD": {"current_value": "positive", "analysis": "MACD is in a positive position, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:11.645880", "processing_time": 7.698857, "llm_used": true}, "processing_time": 7.698857, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 45.956518}}, {"timestamp": "2025-07-06T15:10:13.000966", "output_id": "output_20250706_151013_20eb1028", "input_id": "input_20250706_151003_a4c18677", "prompt_id": "prompt_20250706_151004_08e206fb", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 53, "comment": "Neutral reading, no immediate overbought or oversold conditions."}, "MACD": {"histogram": 0.1, "comment": "Close to zero line, suggesting a sideways trend."}, "Moving_Average": {"50_day": 150.5, "200_day": 153.75, "comment": "Current price near the 50-day moving average, above the 200-day moving average indicating a long-term bullish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.000966", "processing_time": 9.504506, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 53, "comment": "Neutral reading, no immediate overbought or oversold conditions."}, "MACD": {"histogram": 0.1, "comment": "Close to zero line, suggesting a sideways trend."}, "Moving_Average": {"50_day": 150.5, "200_day": 153.75, "comment": "Current price near the 50-day moving average, above the 200-day moving average indicating a long-term bullish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.000966", "processing_time": 9.504506, "llm_used": true}, "processing_time": 9.504506, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 55.461024}}, {"timestamp": "2025-07-06T15:10:13.090896", "output_id": "output_20250706_151013_35e57795", "input_id": "input_20250706_151005_ddc3b58c", "prompt_id": "prompt_20250706_151005_54550153", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "analysis": "Neutral; The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.03, "analysis": "Neutral; The MACD signal line is close to zero, suggesting no clear trend direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "Neutral; The stock is trading slightly below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend with a short-term neutral outlook."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.090896", "processing_time": 9.079444, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "analysis": "Neutral; The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.03, "analysis": "Neutral; The MACD signal line is close to zero, suggesting no clear trend direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "Neutral; The stock is trading slightly below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend with a short-term neutral outlook."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.090896", "processing_time": 9.079444, "llm_used": true}, "processing_time": 9.079444, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 64.540468}}, {"timestamp": "2025-07-06T15:10:13.851243", "output_id": "output_20250706_151013_80281c10", "input_id": "input_20250706_151005_327d2efc", "prompt_id": "prompt_20250706_151005_d2750c56", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.2, "comment": "RSI is in the overbought zone, suggesting a potential pullback or consolidation."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "comment": "The MACD signal line is above the zero line, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "comment": "The stock price is above both the 50-day and 200-day moving averages, suggesting a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.851243", "processing_time": 9.819044, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.2, "comment": "RSI is in the overbought zone, suggesting a potential pullback or consolidation."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "comment": "The MACD signal line is above the zero line, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "comment": "The stock price is above both the 50-day and 200-day moving averages, suggesting a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.851243", "processing_time": 9.819044, "llm_used": true}, "processing_time": 9.819044, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 74.35951200000001}}, {"timestamp": "2025-07-06T15:10:13.995107", "output_id": "output_20250706_151013_4585f398", "input_id": "input_20250706_151004_3a910ebe", "prompt_id": "prompt_20250706_151005_40e8ee15", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.994107", "processing_time": 10.009255, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:13.994107", "processing_time": 10.009255, "llm_used": true}, "processing_time": 10.009255, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 84.368767}}, {"timestamp": "2025-07-06T15:10:14.064328", "output_id": "output_20250706_151014_bde36eb6", "input_id": "input_20250706_151008_425db477", "prompt_id": "prompt_20250706_151008_1a4adfad", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions but the trend is still strong."}, "MACD": {"signal_line": 0.05, "analysis": "MACD is positive and above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.009630", "processing_time": 5.220368, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions but the trend is still strong."}, "MACD": {"signal_line": 0.05, "analysis": "MACD is positive and above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.009630", "processing_time": 5.220368, "llm_used": true}, "processing_time": 5.220368, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 89.589135}}, {"timestamp": "2025-07-06T15:10:14.190470", "output_id": "output_20250706_151014_1809be54", "input_id": "input_20250706_151008_5305b0aa", "prompt_id": "prompt_20250706_151008_df7080dc", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "MACD signal line is positive and above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.190470", "processing_time": 5.955559, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "MACD signal line is positive and above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.190470", "processing_time": 5.955559, "llm_used": true}, "processing_time": 5.955559, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 95.54469399999999}}, {"timestamp": "2025-07-06T15:10:14.449574", "output_id": "output_20250706_151014_64882422", "input_id": "input_20250706_151009_f1c12112", "prompt_id": "prompt_20250706_151009_3bb26274", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD line is above the zero line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.449574", "processing_time": 5.224491, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD line is above the zero line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.449574", "processing_time": 5.224491, "llm_used": true}, "processing_time": 5.224491, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 100.769185}}, {"timestamp": "2025-07-06T15:10:14.972621", "output_id": "output_20250706_151014_704539fb", "input_id": "input_20250706_151009_ffbaeb3c", "prompt_id": "prompt_20250706_151009_894cedb7", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.972621", "processing_time": 5.27469, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:14.972621", "processing_time": 5.27469, "llm_used": true}, "processing_time": 5.27469, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 106.04387499999999}}, {"timestamp": "2025-07-06T15:10:15.091181", "output_id": "output_20250706_151015_b3df580c", "input_id": "input_20250706_151010_7900071f", "prompt_id": "prompt_20250706_151010_f2e09def", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 60, indicating the stock is in an overbought condition but still within the bullish territory."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "analysis": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 130.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:15.091181", "processing_time": 4.902967, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 60, indicating the stock is in an overbought condition but still within the bullish territory."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "analysis": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 130.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:15.091181", "processing_time": 4.902967, "llm_used": true}, "processing_time": 4.902967, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 110.94684199999999}}, {"timestamp": "2025-07-06T15:10:15.668083", "output_id": "output_20250706_151015_9ebadc83", "input_id": "input_20250706_151009_428b467b", "prompt_id": "prompt_20250706_151009_ceb3fee4", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 70, indicating that the stock might be overbought, but the overall trend is bullish."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend. The histogram is positive, confirming the bullish trend."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 125.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:15.668083", "processing_time": 5.796556, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 70, indicating that the stock might be overbought, but the overall trend is bullish."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend. The histogram is positive, confirming the bullish trend."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 125.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:15.668083", "processing_time": 5.796556, "llm_used": true}, "processing_time": 5.796556, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 116.74339799999998}}, {"timestamp": "2025-07-06T15:10:16.347467", "output_id": "output_20250706_151016_96ccf5ec", "input_id": "input_20250706_151009_e98322b5", "prompt_id": "prompt_20250706_151009_aa81955f", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "analysis": "RSI is above 70, indicating that the stock might be overbought, but the overall trend is still bullish."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, showing a bullish trend."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:16.285065", "processing_time": 7.017093, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "analysis": "RSI is above 70, indicating that the stock might be overbought, but the overall trend is still bullish."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, showing a bullish trend."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:16.285065", "processing_time": 7.017093, "llm_used": true}, "processing_time": 7.017093, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 123.76049099999999}}, {"timestamp": "2025-07-06T15:10:28.865105", "output_id": "output_20250706_151028_553614ce", "input_id": "input_20250706_151023_5db65ca7", "prompt_id": "prompt_20250706_151023_1a923442", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:28.865105", "processing_time": 4.940776, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:28.865105", "processing_time": 4.940776, "llm_used": true}, "processing_time": 4.940776, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 128.70126699999997}}, {"timestamp": "2025-07-06T15:10:31.392726", "output_id": "output_20250706_151031_86ae7896", "input_id": "input_20250706_151025_2f3a89a1", "prompt_id": "prompt_20250706_151025_8f618578", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:31.378611", "processing_time": 6.230813, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:31.378611", "processing_time": 6.230813, "llm_used": true}, "processing_time": 6.230813, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 134.93207999999998}}, {"timestamp": "2025-07-06T15:10:32.910585", "output_id": "output_20250706_151032_1285ac3c", "input_id": "input_20250706_151027_babfab08", "prompt_id": "prompt_20250706_151027_dea4aa27", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:32.805394", "processing_time": 5.187008, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:32.805394", "processing_time": 5.187008, "llm_used": true}, "processing_time": 5.187008, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 140.11908799999998}}, {"timestamp": "2025-07-06T15:10:33.124013", "output_id": "output_20250706_151033_e5730422", "input_id": "input_20250706_151025_1c20cbad", "prompt_id": "prompt_20250706_151026_6d110e1f", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69.3, "analysis": "The RSI is above 70, indicating that the stock may be overbought, but the strong bullish news and positive sentiment suggest that the upward trend may continue."}, "MACD": {"signal_line": 10.2, "histogram": 12.4, "analysis": "The MACD line is above the signal line and the histogram is positive, indicating a strong bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:33.045362", "processing_time": 7.102659, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69.3, "analysis": "The RSI is above 70, indicating that the stock may be overbought, but the strong bullish news and positive sentiment suggest that the upward trend may continue."}, "MACD": {"signal_line": 10.2, "histogram": 12.4, "analysis": "The MACD line is above the signal line and the histogram is positive, indicating a strong bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:33.045362", "processing_time": 7.102659, "llm_used": true}, "processing_time": 7.102659, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 147.22174699999997}}, {"timestamp": "2025-07-06T15:10:34.750341", "output_id": "output_20250706_151034_3eaef6ec", "input_id": "input_20250706_151027_ff42d11e", "prompt_id": "prompt_20250706_151027_cc6ff788", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating that the stock is overbought, but the bullish trend suggests that the overbought condition might be temporary."}, "MACD": {"current_value": "positive", "analysis": "The MACD is in positive territory, indicating that the trend is bullish. The histogram is rising, which confirms the bullish signal."}, "Moving_Average": {"short_term_MA": 10, "long_term_MA": 50, "analysis": "The stock is trading above both the short-term and long-term moving averages, suggesting a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:34.734824", "processing_time": 7.460098, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating that the stock is overbought, but the bullish trend suggests that the overbought condition might be temporary."}, "MACD": {"current_value": "positive", "analysis": "The MACD is in positive territory, indicating that the trend is bullish. The histogram is rising, which confirms the bullish signal."}, "Moving_Average": {"short_term_MA": 10, "long_term_MA": 50, "analysis": "The stock is trading above both the short-term and long-term moving averages, suggesting a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:34.734824", "processing_time": 7.460098, "llm_used": true}, "processing_time": 7.460098, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 154.68184499999995}}, {"timestamp": "2025-07-06T15:10:35.447387", "output_id": "output_20250706_151035_22e95e26", "input_id": "input_20250706_151026_ad105c87", "prompt_id": "prompt_20250706_151026_c603f7f3", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69.3, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 125.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:35.333060", "processing_time": 9.314226, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69.3, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 125.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:35.333060", "processing_time": 9.314226, "llm_used": true}, "processing_time": 9.314226, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 163.99607099999994}}, {"timestamp": "2025-07-06T15:10:42.790786", "output_id": "output_20250706_151042_6d19fac7", "input_id": "input_20250706_151036_ec982c84", "prompt_id": "prompt_20250706_151037_966781ce", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:42.790786", "processing_time": 5.982492, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:42.790786", "processing_time": 5.982492, "llm_used": true}, "processing_time": 5.982492, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 169.97856299999995}}, {"timestamp": "2025-07-06T15:10:43.443015", "output_id": "output_20250706_151043_ce0b31ca", "input_id": "input_20250706_151034_43bad826", "prompt_id": "prompt_20250706_151034_2785b4aa", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:43.268057", "processing_time": 8.643416, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:43.268057", "processing_time": 8.643416, "llm_used": true}, "processing_time": 8.643416, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 178.62197899999995}}, {"timestamp": "2025-07-06T15:10:45.813870", "output_id": "output_20250706_151045_b5ede6a0", "input_id": "input_20250706_151039_cd23edae", "prompt_id": "prompt_20250706_151039_ffdbad01", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:45.710789", "processing_time": 5.944161, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:45.710789", "processing_time": 5.944161, "llm_used": true}, "processing_time": 5.944161, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 184.56613999999996}}, {"timestamp": "2025-07-06T15:10:46.345933", "output_id": "output_20250706_151046_1badcd5a", "input_id": "input_20250706_151040_1d999d30", "prompt_id": "prompt_20250706_151040_b34baaae", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:46.345933", "processing_time": 6.202677, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:46.345933", "processing_time": 6.202677, "llm_used": true}, "processing_time": 6.202677, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 190.76881699999996}}, {"timestamp": "2025-07-06T15:10:47.710270", "output_id": "output_20250706_151047_bb4b3c36", "input_id": "input_20250706_151037_0b9de879", "prompt_id": "prompt_20250706_151037_c82e50f1", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "current_price_above_50_day_MA": true, "current_price_above_200_day_MA": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:47.710270", "processing_time": 10.749841, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "current_price_above_50_day_MA": true, "current_price_above_200_day_MA": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:47.710270", "processing_time": 10.749841, "llm_used": true}, "processing_time": 10.749841, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 201.51865799999996}}, {"timestamp": "2025-07-06T15:10:54.790867", "output_id": "output_20250706_151054_0c7a7f23", "input_id": "input_20250706_151048_664d680f", "prompt_id": "prompt_20250706_151048_49d54c83", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "interpretation": "positive trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:54.790867", "processing_time": 6.294004, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "interpretation": "positive trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:54.790867", "processing_time": 6.294004, "llm_used": true}, "processing_time": 6.294004, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 207.81266199999996}}, {"timestamp": "2025-07-06T15:10:55.017471", "output_id": "output_20250706_151055_e4185acb", "input_id": "input_20250706_151050_5e24a8dd", "prompt_id": "prompt_20250706_151050_4abeb8a7", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:55.017471", "processing_time": 4.72192, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:55.017471", "processing_time": 4.72192, "llm_used": true}, "processing_time": 4.72192, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 212.53458199999997}}, {"timestamp": "2025-07-06T15:10:56.109089", "output_id": "output_20250706_151056_24a1afd3", "input_id": "input_20250706_151047_33caabce", "prompt_id": "prompt_20250706_151047_2a2f3d68", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:56.109089", "processing_time": 8.398819, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:56.109089", "processing_time": 8.398819, "llm_used": true}, "processing_time": 8.398819, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 220.93340099999998}}, {"timestamp": "2025-07-06T15:10:57.083325", "output_id": "output_20250706_151057_41a47712", "input_id": "input_20250706_151047_022340c2", "prompt_id": "prompt_20250706_151047_a214d559", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but the bullish trend suggests this may be a temporary condition."}, "MACD": {"current_value": 0.1, "analysis": "The MACD line is above the signal line, indicating a bullish trend. The histogram is positive, suggesting upward momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:57.082216", "processing_time": 9.672787, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but the bullish trend suggests this may be a temporary condition."}, "MACD": {"current_value": 0.1, "analysis": "The MACD line is above the signal line, indicating a bullish trend. The histogram is positive, suggesting upward momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:10:57.082216", "processing_time": 9.672787, "llm_used": true}, "processing_time": 9.672787, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 230.60618799999997}}, {"timestamp": "2025-07-06T15:11:36.396621", "output_id": "output_20250706_151136_ccf79cb6", "input_id": "input_20250706_151131_10de2b89", "prompt_id": "prompt_20250706_151131_e1a0cca0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 70, "analysis": "RSI is overbought, suggesting a potential pullback."}, "MACD": {"signal_line": 100, "analysis": "MACD line is above the zero line, indicating a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:11:36.396621", "processing_time": 4.955225, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 70, "analysis": "RSI is overbought, suggesting a potential pullback."}, "MACD": {"signal_line": 100, "analysis": "MACD line is above the zero line, indicating a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:11:36.396621", "processing_time": 4.955225, "llm_used": true}, "processing_time": 4.955225, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 4.955225}}, {"timestamp": "2025-07-06T15:12:24.946895", "output_id": "output_20250706_151224_c282cdcc", "input_id": "input_20250706_151219_719a2ce3", "prompt_id": "prompt_20250706_151220_0dc49f39", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:24.841476", "processing_time": 6.825962, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:24.841476", "processing_time": 6.825962, "llm_used": true}, "processing_time": 6.825962, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 11.781187}}, {"timestamp": "2025-07-06T15:12:25.828625", "output_id": "output_20250706_151225_f38e249d", "input_id": "input_20250706_151218_7cd22baf", "prompt_id": "prompt_20250706_151220_148bab8e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating that the stock is in a bullish trend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:25.781369", "processing_time": 8.071971, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating that the stock is in a bullish trend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:25.781369", "processing_time": 8.071971, "llm_used": true}, "processing_time": 8.071971, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 19.853158}}, {"timestamp": "2025-07-06T15:12:26.473486", "output_id": "output_20250706_151226_b8632670", "input_id": "input_20250706_151219_2134d8ed", "prompt_id": "prompt_20250706_151220_70b6c33c", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD signal line is just above the zero line, suggesting a stable trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a short-term bullish trend with a longer-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:26.473486", "processing_time": 8.564925, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD signal line is just above the zero line, suggesting a stable trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a short-term bullish trend with a longer-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:26.473486", "processing_time": 8.564925, "llm_used": true}, "processing_time": 8.564925, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 28.418083000000003}}, {"timestamp": "2025-07-06T15:12:26.836033", "output_id": "output_20250706_151226_73af5485", "input_id": "input_20250706_151219_54534bc8", "prompt_id": "prompt_20250706_151220_eb460628", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:26.836033", "processing_time": 8.832014, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:26.836033", "processing_time": 8.832014, "llm_used": true}, "processing_time": 8.832014, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 37.250097000000004}}, {"timestamp": "2025-07-06T15:12:27.023184", "output_id": "output_20250706_151227_7e34a1b1", "input_id": "input_20250706_151219_198d68f2", "prompt_id": "prompt_20250706_151220_a9c4b3e7", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:27.023184", "processing_time": 9.062728, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:27.023184", "processing_time": 9.062728, "llm_used": true}, "processing_time": 9.062728, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 46.312825000000004}}, {"timestamp": "2025-07-06T15:12:27.250555", "output_id": "output_20250706_151227_8c41f3f5", "input_id": "input_20250706_151222_6804d30e", "prompt_id": "prompt_20250706_151222_1fe48e1b", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, suggesting a possible pullback"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "Positive crossover, indicating bullish momentum"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 180.0, "interpretation": "Price above both moving averages, suggesting a strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:27.234790", "processing_time": 4.922452, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, suggesting a possible pullback"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "Positive crossover, indicating bullish momentum"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 180.0, "interpretation": "Price above both moving averages, suggesting a strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:27.234790", "processing_time": 4.922452, "llm_used": true}, "processing_time": 4.922452, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 51.235277}}, {"timestamp": "2025-07-06T15:12:27.607780", "output_id": "output_20250706_151227_3a394bcf", "input_id": "input_20250706_151218_f25d5941", "prompt_id": "prompt_20250706_151220_bed3c937", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:27.607780", "processing_time": 9.791382, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:27.607780", "processing_time": 9.791382, "llm_used": true}, "processing_time": 9.791382, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 61.026659}}, {"timestamp": "2025-07-06T15:12:28.064972", "output_id": "output_20250706_151228_90d8527e", "input_id": "input_20250706_151219_eea3208e", "prompt_id": "prompt_20250706_151220_0eb90a81", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.002512", "processing_time": 10.054097, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.002512", "processing_time": 10.054097, "llm_used": true}, "processing_time": 10.054097, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 71.08075600000001}}, {"timestamp": "2025-07-06T15:12:28.078393", "output_id": "output_20250706_151228_42eced43", "input_id": "input_20250706_151217_a1830df7", "prompt_id": "prompt_20250706_151217_42545fef", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 60, indicating the stock is in an uptrend with some momentum."}, "MACD": {"signal_line": 0.005, "histogram": 0.012, "analysis": "The MACD line is above the signal line, with a positive histogram, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, suggesting a long-term uptrend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.012287", "processing_time": 10.595519, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 60, indicating the stock is in an uptrend with some momentum."}, "MACD": {"signal_line": 0.005, "histogram": 0.012, "analysis": "The MACD line is above the signal line, with a positive histogram, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, suggesting a long-term uptrend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.012287", "processing_time": 10.595519, "llm_used": true}, "processing_time": 10.595519, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 81.676275}}, {"timestamp": "2025-07-06T15:12:28.349573", "output_id": "output_20250706_151228_78e6c6f6", "input_id": "input_20250706_151218_37483cf1", "prompt_id": "prompt_20250706_151220_65639e0c", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "MA": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.272317", "processing_time": 10.492748, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "MA": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.272317", "processing_time": 10.492748, "llm_used": true}, "processing_time": 10.492748, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 92.16902300000001}}, {"timestamp": "2025-07-06T15:12:28.844683", "output_id": "output_20250706_151228_77d8985b", "input_id": "input_20250706_151218_44966c35", "prompt_id": "prompt_20250706_151220_f0a8342d", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "positive crossover"}, "Moving_Average": {"50_day": 152.0, "200_day": 145.0, "interpretation": "bullish trend confirmed"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.844683", "processing_time": 11.10471, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "positive crossover"}, "Moving_Average": {"50_day": 152.0, "200_day": 145.0, "interpretation": "bullish trend confirmed"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:28.844683", "processing_time": 11.10471, "llm_used": true}, "processing_time": 11.10471, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 103.27373300000001}}, {"timestamp": "2025-07-06T15:12:30.658556", "output_id": "output_20250706_151230_1bd85db8", "input_id": "input_20250706_151223_5dd96693", "prompt_id": "prompt_20250706_151223_f5c4776b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in an uptrend."}, "MACD": {"signal_line": 15, "histogram": 0.5, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 150, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:30.658556", "processing_time": 6.978398, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in an uptrend."}, "MACD": {"signal_line": 15, "histogram": 0.5, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 150, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:30.658556", "processing_time": 6.978398, "llm_used": true}, "processing_time": 6.978398, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 110.252131}}, {"timestamp": "2025-07-06T15:12:31.450732", "output_id": "output_20250706_151231_f8231dfd", "input_id": "input_20250706_151225_7f1c42c3", "prompt_id": "prompt_20250706_151225_a8f4076e", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "interpretation": " bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "interpretation": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:31.417095", "processing_time": 5.994735, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "interpretation": " bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "interpretation": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:31.417095", "processing_time": 5.994735, "llm_used": true}, "processing_time": 5.994735, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 116.24686600000001}}, {"timestamp": "2025-07-06T15:12:31.875660", "output_id": "output_20250706_151231_e34ad1c3", "input_id": "input_20250706_151224_cca8854a", "prompt_id": "prompt_20250706_151224_1201084e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions, but the trend is strong."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive signal, suggesting upward momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:31.875660", "processing_time": 7.402224, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions, but the trend is strong."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive signal, suggesting upward momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:31.875660", "processing_time": 7.402224, "llm_used": true}, "processing_time": 7.402224, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 123.64909000000002}}, {"timestamp": "2025-07-06T15:12:33.776889", "output_id": "output_20250706_151233_723153dd", "input_id": "input_20250706_151227_dec146ba", "prompt_id": "prompt_20250706_151227_6623b3b6", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above 50-day and 200-day moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:33.749747", "processing_time": 6.604491, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above 50-day and 200-day moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:33.749747", "processing_time": 6.604491, "llm_used": true}, "processing_time": 6.604491, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 130.25358100000003}}, {"timestamp": "2025-07-06T15:12:34.796886", "output_id": "output_20250706_151234_d826da1e", "input_id": "input_20250706_151224_59856eed", "prompt_id": "prompt_20250706_151224_1b918054", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": {"bullish": true}}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "cross_over": {"50_day_over_200_day": true}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:34.796886", "processing_time": 10.668531, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": {"bullish": true}}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "cross_over": {"50_day_over_200_day": true}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:34.796886", "processing_time": 10.668531, "llm_used": true}, "processing_time": 10.668531, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 140.92211200000003}}, {"timestamp": "2025-07-06T15:12:37.255783", "output_id": "output_20250706_151237_fabbf104", "input_id": "input_20250706_151226_5f86c3d2", "prompt_id": "prompt_20250706_151226_2a42f1d6", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:37.117049", "processing_time": 10.510429, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:37.117049", "processing_time": 10.510429, "llm_used": true}, "processing_time": 10.510429, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 151.43254100000001}}, {"timestamp": "2025-07-06T15:12:46.740911", "output_id": "output_20250706_151246_8ff3e386", "input_id": "input_20250706_151241_3acd9094", "prompt_id": "prompt_20250706_151241_7fe80f95", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:46.646702", "processing_time": 5.680868, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:46.646702", "processing_time": 5.680868, "llm_used": true}, "processing_time": 5.680868, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 157.11340900000002}}, {"timestamp": "2025-07-06T15:12:47.412780", "output_id": "output_20250706_151247_b2048394", "input_id": "input_20250706_151241_ede6e1bb", "prompt_id": "prompt_20250706_151241_7c4f0874", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD line is above the signal line, showing a bullish trend."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:47.412780", "processing_time": 5.976594, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD line is above the signal line, showing a bullish trend."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:47.412780", "processing_time": 5.976594, "llm_used": true}, "processing_time": 5.976594, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 163.09000300000002}}, {"timestamp": "2025-07-06T15:12:49.730979", "output_id": "output_20250706_151249_a06fe5cd", "input_id": "input_20250706_151243_107ceb68", "prompt_id": "prompt_20250706_151244_7c2a70c8", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "analysis": "The MACD histogram is rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:49.729975", "processing_time": 5.80942, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "analysis": "The MACD histogram is rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:49.729975", "processing_time": 5.80942, "llm_used": true}, "processing_time": 5.80942, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 168.899423}}, {"timestamp": "2025-07-06T15:12:50.164963", "output_id": "output_20250706_151250_560b1ab4", "input_id": "input_20250706_151244_dc97e88d", "prompt_id": "prompt_20250706_151244_8ef6914c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:50.152627", "processing_time": 5.610948, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:50.152627", "processing_time": 5.610948, "llm_used": true}, "processing_time": 5.610948, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 174.51037100000002}}, {"timestamp": "2025-07-06T15:12:50.849464", "output_id": "output_20250706_151250_f5a7f4f7", "input_id": "input_20250706_151245_004ab43f", "prompt_id": "prompt_20250706_151245_8e4913f0", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock might be overbought."}, "MACD": {"signal_line": 20, "analysis": "The MACD is positive and the signal line is above the MACD line, suggesting bullish momentum."}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 148.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:50.667227", "processing_time": 4.824679, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock might be overbought."}, "MACD": {"signal_line": 20, "analysis": "The MACD is positive and the signal line is above the MACD line, suggesting bullish momentum."}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 148.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:50.667227", "processing_time": 4.824679, "llm_used": true}, "processing_time": 4.824679, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 179.33505000000002}}, {"timestamp": "2025-07-06T15:12:51.510505", "output_id": "output_20250706_151251_581964f3", "input_id": "input_20250706_151243_c141f64b", "prompt_id": "prompt_20250706_151243_3bc467e0", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 70, indicating the stock is overbought, but the trend is strong."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD signal line is above the zero line and the histogram is rising, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The 50-day moving average is above the 200-day moving average, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:51.236846", "processing_time": 7.903834, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 70, indicating the stock is overbought, but the trend is strong."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD signal line is above the zero line and the histogram is rising, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The 50-day moving average is above the 200-day moving average, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:51.236846", "processing_time": 7.903834, "llm_used": true}, "processing_time": 7.903834, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 187.238884}}, {"timestamp": "2025-07-06T15:12:56.451501", "output_id": "output_20250706_151256_4f0480aa", "input_id": "input_20250706_151250_51748898", "prompt_id": "prompt_20250706_151250_058fb64d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:56.450501", "processing_time": 6.285538, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:56.450501", "processing_time": 6.285538, "llm_used": true}, "processing_time": 6.285538, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 193.52442200000002}}, {"timestamp": "2025-07-06T15:12:58.101472", "output_id": "output_20250706_151258_2d58dc01", "input_id": "input_20250706_151253_67bc48a0", "prompt_id": "prompt_20250706_151253_275e411c", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "overbought": false, "overbought_threshold": 70}, "MACD": {"signal_line": 0.002, "histogram": 0.003, "crossover": "bullish"}, "moving_averages": {"50_day_MA": 155.0, "200_day_MA": 160.0, "current_price": 165.0}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:57.979342", "processing_time": 4.45369, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "overbought": false, "overbought_threshold": 70}, "MACD": {"signal_line": 0.002, "histogram": 0.003, "crossover": "bullish"}, "moving_averages": {"50_day_MA": 155.0, "200_day_MA": 160.0, "current_price": 165.0}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:12:57.979342", "processing_time": 4.45369, "llm_used": true}, "processing_time": 4.45369, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 197.978112}}, {"timestamp": "2025-07-06T15:13:04.565787", "output_id": "output_20250706_151304_6ba8cd02", "input_id": "input_20250706_151257_712706f4", "prompt_id": "prompt_20250706_151258_277bdc0d", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "interpretation": "50-day moving average above 200-day, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:04.397043", "processing_time": 6.550749, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "interpretation": "50-day moving average above 200-day, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:04.397043", "processing_time": 6.550749, "llm_used": true}, "processing_time": 6.550749, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 204.528861}}, {"timestamp": "2025-07-06T15:13:05.297628", "output_id": "output_20250706_151305_7839a047", "input_id": "input_20250706_151257_b73b4444", "prompt_id": "prompt_20250706_151257_c8189e0d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68.5, "analysis": "The RSI is above 70, indicating that the stock is overbought but still in a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "analysis": "The MACD signal line is above the zero line with a rising histogram, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:05.297628", "processing_time": 7.803721, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68.5, "analysis": "The RSI is above 70, indicating that the stock is overbought but still in a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "analysis": "The MACD signal line is above the zero line with a rising histogram, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:05.297628", "processing_time": 7.803721, "llm_used": true}, "processing_time": 7.803721, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 212.332582}}, {"timestamp": "2025-07-06T15:13:07.440758", "output_id": "output_20250706_151307_02722281", "input_id": "input_20250706_151253_030d0e10", "prompt_id": "prompt_20250706_151253_815b6a6c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "above 50-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:07.118795", "processing_time": 13.810166, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "above 50-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:07.118795", "processing_time": 13.810166, "llm_used": true}, "processing_time": 13.810166, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 226.142748}}, {"timestamp": "2025-07-06T15:13:12.159991", "output_id": "output_20250706_151312_3eabf3ed", "input_id": "input_20250706_151307_6bd67474", "prompt_id": "prompt_20250706_151308_647fc51b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "price above long-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:12.157992", "processing_time": 4.687462, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "price above long-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:12.157992", "processing_time": 4.687462, "llm_used": true}, "processing_time": 4.687462, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 230.83021000000002}}, {"timestamp": "2025-07-06T15:13:14.122380", "output_id": "output_20250706_151314_d2864efd", "input_id": "input_20250706_151306_7d18ff96", "prompt_id": "prompt_20250706_151307_e3cf11ce", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought, but the overall trend is bullish."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:14.046878", "processing_time": 7.149652, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought, but the overall trend is bullish."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:14.046878", "processing_time": 7.149652, "llm_used": true}, "processing_time": 7.149652, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 237.97986200000003}}, {"timestamp": "2025-07-06T15:13:14.794856", "output_id": "output_20250706_151314_713026c7", "input_id": "input_20250706_151308_ce79c11d", "prompt_id": "prompt_20250706_151308_00b1a0ea", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:14.405596", "processing_time": 5.67643, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:14.405596", "processing_time": 5.67643, "llm_used": true}, "processing_time": 5.67643, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 243.65629200000004}}, {"timestamp": "2025-07-06T15:13:15.274765", "output_id": "output_20250706_151315_98b7edb5", "input_id": "input_20250706_151310_c64c9c51", "prompt_id": "prompt_20250706_151310_8365a1ea", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is in the overbought territory but has not shown a strong sell signal yet."}, "MACD": {"current_value": "0.10", "analysis": "The MACD line is above the signal line, indicating bullish momentum."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 120.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:15.107536", "processing_time": 4.271374, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is in the overbought territory but has not shown a strong sell signal yet."}, "MACD": {"current_value": "0.10", "analysis": "The MACD line is above the signal line, indicating bullish momentum."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 120.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:13:15.107536", "processing_time": 4.271374, "llm_used": true}, "processing_time": 4.271374, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 247.92766600000004}}, {"timestamp": "2025-07-06T15:29:12.310259", "output_id": "output_20250706_152912_798147cb", "input_id": "input_20250706_152906_22c00b2e", "prompt_id": "prompt_20250706_152907_637a2f95", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, indicating bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:12.310259", "processing_time": 6.109527, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, indicating bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:12.310259", "processing_time": 6.109527, "llm_used": true}, "processing_time": 6.109527, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 10.770582000000001}}, {"timestamp": "2025-07-06T15:29:12.974972", "output_id": "output_20250706_152912_3dd0342f", "input_id": "input_20250706_152905_bc1f3085", "prompt_id": "prompt_20250706_152906_e3bf6a46", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.25, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "comment": "接近中性区域，但尚未进入超买/超卖区间"}, "MACD": {"signal_line": -0.2, "histogram": -0.05, "comment": "MACD 线接近零轴，没有明确的方向信号"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "comment": "50日均线略高于当前价格，200日均线远高于当前价格，显示长期趋势向上但短期内波动较大"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:12.825724", "processing_time": 6.826595, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.25, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "comment": "接近中性区域，但尚未进入超买/超卖区间"}, "MACD": {"signal_line": -0.2, "histogram": -0.05, "comment": "MACD 线接近零轴，没有明确的方向信号"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "comment": "50日均线略高于当前价格，200日均线远高于当前价格，显示长期趋势向上但短期内波动较大"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:12.825724", "processing_time": 6.826595, "llm_used": true}, "processing_time": 6.826595, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 17.597177000000002}}, {"timestamp": "2025-07-06T15:29:14.065122", "output_id": "output_20250706_152914_dd1e53cc", "input_id": "input_20250706_152908_6074a684", "prompt_id": "prompt_20250706_152909_bd5c0fe9", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "Overbought, indicating potential price correction."}, "MACD": {"signal_line": 10, "histogram": 5, "interpretation": "MACD is above the signal line and positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 150, "200_day_MA": 140, "interpretation": "Stock price above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:14.065122", "processing_time": 7.220475, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "Overbought, indicating potential price correction."}, "MACD": {"signal_line": 10, "histogram": 5, "interpretation": "MACD is above the signal line and positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 150, "200_day_MA": 140, "interpretation": "Stock price above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:14.065122", "processing_time": 7.220475, "llm_used": true}, "processing_time": 7.220475, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 24.817652000000002}}, {"timestamp": "2025-07-06T15:29:15.103492", "output_id": "output_20250706_152915_d0eea59d", "input_id": "input_20250706_152908_1dc54125", "prompt_id": "prompt_20250706_152909_9021a573", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70.5, "overbought": true}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "trend": "positive"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:15.103492", "processing_time": 8.149254, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70.5, "overbought": true}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "trend": "positive"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:15.103492", "processing_time": 8.149254, "llm_used": true}, "processing_time": 8.149254, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 32.966906}}, {"timestamp": "2025-07-06T15:29:15.528169", "output_id": "output_20250706_152915_f862a970", "input_id": "input_20250706_152908_4bfa0e9f", "prompt_id": "prompt_20250706_152909_95bdfd27", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:15.528169", "processing_time": 8.727529, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:15.528169", "processing_time": 8.727529, "llm_used": true}, "processing_time": 8.727529, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 41.694435}}, {"timestamp": "2025-07-06T15:29:15.944704", "output_id": "output_20250706_152915_75aae8d7", "input_id": "input_20250706_152907_aeeaf726", "prompt_id": "prompt_20250706_152909_bb1f6ac4", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions, but the trend is strong."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "MACD is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:15.942700", "processing_time": 9.404781, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions, but the trend is strong."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "MACD is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:15.942700", "processing_time": 9.404781, "llm_used": true}, "processing_time": 9.404781, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 51.099216}}, {"timestamp": "2025-07-06T15:29:16.286685", "output_id": "output_20250706_152916_e46f30ab", "input_id": "input_20250706_152908_be30fc1e", "prompt_id": "prompt_20250706_152909_8f659c7a", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70.5, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": " bullish trend"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above 50-day MA and 200-day MA, indicating a strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.286685", "processing_time": 9.402514, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70.5, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": " bullish trend"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above 50-day MA and 200-day MA, indicating a strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.286685", "processing_time": 9.402514, "llm_used": true}, "processing_time": 9.402514, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 60.501729999999995}}, {"timestamp": "2025-07-06T15:29:16.418412", "output_id": "output_20250706_152916_b5816099", "input_id": "input_20250706_152907_1c19b8b1", "prompt_id": "prompt_20250706_152909_4cec441f", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "MACD is positive and rising, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.344616", "processing_time": 9.611711, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "MACD is positive and rising, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.344616", "processing_time": 9.611711, "llm_used": true}, "processing_time": 9.611711, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 70.113441}}, {"timestamp": "2025-07-06T15:29:16.599983", "output_id": "output_20250706_152916_1774b426", "input_id": "input_20250706_152911_c1d6ee68", "prompt_id": "prompt_20250706_152911_743330bf", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.515649", "processing_time": 4.79727, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.515649", "processing_time": 4.79727, "llm_used": true}, "processing_time": 4.79727, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 74.91071099999999}}, {"timestamp": "2025-07-06T15:29:16.986315", "output_id": "output_20250706_152916_34934a16", "input_id": "input_20250706_152912_e3d6cf41", "prompt_id": "prompt_20250706_152912_32f78bf0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 10, "histogram": {"current": 0.5, "previous": -0.2}, "signal": "bullish crossover"}, "MA": {"50_day_MA": 155.0, "200_day_MA": 120.0, "current_price": 160.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.985308", "processing_time": 4.904841, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 10, "histogram": {"current": 0.5, "previous": -0.2}, "signal": "bullish crossover"}, "MA": {"50_day_MA": 155.0, "200_day_MA": 120.0, "current_price": 160.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:16.985308", "processing_time": 4.904841, "llm_used": true}, "processing_time": 4.904841, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 79.815552}}, {"timestamp": "2025-07-06T15:29:17.141002", "output_id": "output_20250706_152917_a32980f4", "input_id": "input_20250706_152912_57de40b7", "prompt_id": "prompt_20250706_152912_7966a4b5", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:17.008267", "processing_time": 4.423592, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:17.008267", "processing_time": 4.423592, "llm_used": true}, "processing_time": 4.423592, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 84.239144}}, {"timestamp": "2025-07-06T15:29:17.646775", "output_id": "output_20250706_152917_0a4cdd9f", "input_id": "input_20250706_152909_bd076fc2", "prompt_id": "prompt_20250706_152909_5765664b", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "indicating neutral market conditions"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "indicating a slight bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "50-day MA crossing above 200-day MA, suggesting a bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:17.620945", "processing_time": 10.644663, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "indicating neutral market conditions"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "indicating a slight bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "50-day MA crossing above 200-day MA, suggesting a bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:17.620945", "processing_time": 10.644663, "llm_used": true}, "processing_time": 10.644663, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 94.88380699999999}}, {"timestamp": "2025-07-06T15:29:17.948884", "output_id": "output_20250706_152917_116c2d49", "input_id": "input_20250706_152911_4790ea87", "prompt_id": "prompt_20250706_152911_5f48dc2c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating the stock is in a state of overbought but still within a healthy range."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:17.750352", "processing_time": 5.920094, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating the stock is in a state of overbought but still within a healthy range."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:17.750352", "processing_time": 5.920094, "llm_used": true}, "processing_time": 5.920094, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 100.803901}}, {"timestamp": "2025-07-06T15:29:19.010388", "output_id": "output_20250706_152919_3a4721fe", "input_id": "input_20250706_152909_0125dc62", "prompt_id": "prompt_20250706_152909_e74dd930", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero": true, "signal": "bullish"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:19.010388", "processing_time": 12.034106, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero": true, "signal": "bullish"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:19.010388", "processing_time": 12.034106, "llm_used": true}, "processing_time": 12.034106, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 112.83800699999999}}, {"timestamp": "2025-07-06T15:29:20.066927", "output_id": "output_20250706_152920_3435eeca", "input_id": "input_20250706_152914_7884bd2e", "prompt_id": "prompt_20250706_152914_fea122aa", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:20.050008", "processing_time": 5.412769, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:20.050008", "processing_time": 5.412769, "llm_used": true}, "processing_time": 5.412769, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 118.25077599999999}}, {"timestamp": "2025-07-06T15:29:21.431030", "output_id": "output_20250706_152921_afed9796", "input_id": "input_20250706_152914_f78b6319", "prompt_id": "prompt_20250706_152914_a3780443", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI indicates that the stock is in a state of slight overbought, but not excessively so."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "The MACD histogram is rising, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The 50-day moving average is above the 200-day moving average, indicating a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:20.953491", "processing_time": 6.737367, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI indicates that the stock is in a state of slight overbought, but not excessively so."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "The MACD histogram is rising, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The 50-day moving average is above the 200-day moving average, indicating a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:20.953491", "processing_time": 6.737367, "llm_used": true}, "processing_time": 6.737367, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 124.988143}}, {"timestamp": "2025-07-06T15:29:25.738205", "output_id": "output_20250706_152925_caa5c832", "input_id": "input_20250706_152913_90bb1c1f", "prompt_id": "prompt_20250706_152914_679f319e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 125.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:25.567903", "processing_time": 11.952787, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 125.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:25.567903", "processing_time": 11.952787, "llm_used": true}, "processing_time": 11.952787, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 136.94092999999998}}, {"timestamp": "2025-07-06T15:29:32.984228", "output_id": "output_20250706_152932_60faaee2", "input_id": "input_20250706_152927_7e6d3a6a", "prompt_id": "prompt_20250706_152928_497278bc", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:32.984228", "processing_time": 5.415913, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:32.984228", "processing_time": 5.415913, "llm_used": true}, "processing_time": 5.415913, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 142.35684299999997}}, {"timestamp": "2025-07-06T15:29:40.444996", "output_id": "output_20250706_152940_5beb0d9b", "input_id": "input_20250706_152934_e7858449", "prompt_id": "prompt_20250706_152934_1955575e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69.3, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:39.600596", "processing_time": 4.979101, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69.3, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:39.600596", "processing_time": 4.979101, "llm_used": true}, "processing_time": 4.979101, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 147.33594399999998}}, {"timestamp": "2025-07-06T15:29:40.695016", "output_id": "output_20250706_152940_1b2e265f", "input_id": "input_20250706_152934_e5d3dfd7", "prompt_id": "prompt_20250706_152934_254059fe", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:40.062802", "processing_time": 5.834181, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:40.062802", "processing_time": 5.834181, "llm_used": true}, "processing_time": 5.834181, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 153.17012499999998}}, {"timestamp": "2025-07-06T15:29:42.381188", "output_id": "output_20250706_152942_8102150e", "input_id": "input_20250706_152933_e557f9e3", "prompt_id": "prompt_20250706_152933_fcba094e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating that the stock may be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "The MACD histogram is positive and increasing, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:42.331237", "processing_time": 8.50739, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating that the stock may be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "The MACD histogram is positive and increasing, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:42.331237", "processing_time": 8.50739, "llm_used": true}, "processing_time": 8.50739, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 161.67751499999997}}, {"timestamp": "2025-07-06T15:29:43.542625", "output_id": "output_20250706_152943_59fc1d47", "input_id": "input_20250706_152937_086f988a", "prompt_id": "prompt_20250706_152938_cdb8cc45", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:43.542625", "processing_time": 6.096938, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:43.542625", "processing_time": 6.096938, "llm_used": true}, "processing_time": 6.096938, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 167.77445299999997}}, {"timestamp": "2025-07-06T15:29:44.109497", "output_id": "output_20250706_152944_e6ee7330", "input_id": "input_20250706_152937_d15c2eb8", "prompt_id": "prompt_20250706_152938_c058136a", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:43.912894", "processing_time": 6.534131, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:43.912894", "processing_time": 6.534131, "llm_used": true}, "processing_time": 6.534131, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 174.30858399999997}}, {"timestamp": "2025-07-06T15:29:44.855312", "output_id": "output_20250706_152944_6d639ae6", "input_id": "input_20250706_152938_cc2909a2", "prompt_id": "prompt_20250706_152939_ce9b51ad", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 0.5, "analysis": "MACD is above the signal line with a positive histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 150, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:44.851785", "processing_time": 6.361703, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 0.5, "analysis": "MACD is above the signal line with a positive histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 150, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:44.851785", "processing_time": 6.361703, "llm_used": true}, "processing_time": 6.361703, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 180.67028699999997}}, {"timestamp": "2025-07-06T15:29:45.949692", "output_id": "output_20250706_152945_967e76f9", "input_id": "input_20250706_152939_d4bb394f", "prompt_id": "prompt_20250706_152939_78107844", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.03, "analysis": "MACD line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:45.829128", "processing_time": 7.256331, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.03, "analysis": "MACD line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:45.829128", "processing_time": 7.256331, "llm_used": true}, "processing_time": 7.256331, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 187.92661799999996}}, {"timestamp": "2025-07-06T15:29:48.583053", "output_id": "output_20250706_152948_9bc2f66e", "input_id": "input_20250706_152941_08c37564", "prompt_id": "prompt_20250706_152941_a0c88418", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "MACD is above the signal line and the histogram is rising, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:48.495821", "processing_time": 7.828356, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "MACD is above the signal line and the histogram is rising, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:48.495821", "processing_time": 7.828356, "llm_used": true}, "processing_time": 7.828356, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 195.75497399999998}}, {"timestamp": "2025-07-06T15:29:49.980945", "output_id": "output_20250706_152949_51c8cf75", "input_id": "input_20250706_152942_5a13b693", "prompt_id": "prompt_20250706_152943_99b0981a", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:49.928709", "processing_time": 6.931835, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:49.928709", "processing_time": 6.931835, "llm_used": true}, "processing_time": 6.931835, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 202.68680899999998}}, {"timestamp": "2025-07-06T15:29:51.357284", "output_id": "output_20250706_152951_2ce0d464", "input_id": "input_20250706_152941_157cf9b6", "prompt_id": "prompt_20250706_152941_95e5f998", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line_cross": "bullish", "histogram": "positive trend"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:51.357284", "processing_time": 10.715994, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line_cross": "bullish", "histogram": "positive trend"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:51.357284", "processing_time": 10.715994, "llm_used": true}, "processing_time": 10.715994, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 213.40280299999998}}, {"timestamp": "2025-07-06T15:29:54.656433", "output_id": "output_20250706_152954_4650d625", "input_id": "input_20250706_152948_14621670", "prompt_id": "prompt_20250706_152948_5e934c61", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought, but the positive news could be supporting the price."}, "MACD": {"signal_line": 0.01, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:53.851691", "processing_time": 5.442416, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought, but the positive news could be supporting the price."}, "MACD": {"signal_line": 0.01, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:53.851691", "processing_time": 5.442416, "llm_used": true}, "processing_time": 5.442416, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 218.845219}}, {"timestamp": "2025-07-06T15:29:59.195138", "output_id": "output_20250706_152959_eaf00cc7", "input_id": "input_20250706_152952_c7a8d697", "prompt_id": "prompt_20250706_152952_7b7e238a", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:59.053698", "processing_time": 7.408023, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:29:59.053698", "processing_time": 7.408023, "llm_used": true}, "processing_time": 7.408023, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 226.253242}}, {"timestamp": "2025-07-06T15:30:00.947663", "output_id": "output_20250706_153000_6b8bd360", "input_id": "input_20250706_152953_2db78fcf", "prompt_id": "prompt_20250706_152954_d4b9769b", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:30:00.947663", "processing_time": 8.447014, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:30:00.947663", "processing_time": 8.447014, "llm_used": true}, "processing_time": 8.447014, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 234.700256}}, {"timestamp": "2025-07-06T15:30:03.241069", "output_id": "output_20250706_153003_11900dcb", "input_id": "input_20250706_152958_027d381e", "prompt_id": "prompt_20250706_152958_835b3f89", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:30:03.241069", "processing_time": 5.143977, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T15:30:03.241069", "processing_time": 5.143977, "llm_used": true}, "processing_time": 5.143977, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 239.844233}}]