2025-07-06 16:02:25,587 - __main__ - INFO - ============================================================
2025-07-06 16:02:25,587 - __main__ - INFO - 开始OPRO系统修复验证测试
2025-07-06 16:02:25,587 - __main__ - INFO - ============================================================
2025-07-06 16:02:25,587 - __main__ - INFO - 
========================================
2025-07-06 16:02:25,587 - __main__ - INFO - 测试1：基本功能测试
2025-07-06 16:02:25,587 - __main__ - INFO - ========================================
2025-07-06 16:02:25,588 - __main__ - INFO - 开始测试增强的周期性OPRO管理器
2025-07-06 16:02:25,588 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:02:25,589 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 16:02:25,589 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 16:02:25,589 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:02:25,590 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 16:02:25,623 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 16:02:25,624 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 16:02:25,625 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 16:02:25,626 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 16:02:25,626 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 16:02:25,627 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 16:02:25,627 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 16:02:25,627 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:02:25,628 - __main__ - INFO - 加载历史数据完成: 0 个实验记录
2025-07-06 16:02:25,628 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 16:02:25,628 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 16:02:25,628 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 16:02:25,628 - __main__ - INFO - ✅ 管理器创建成功
2025-07-06 16:02:25,628 - __main__ - INFO - 测试A/B测试数据收集...
2025-07-06 16:02:25,629 - __main__ - INFO - A/B测试数据: {'week_number': 1, 'test_agents': [], 'original_performance': {}, 'optimized_performance': {}, 'data_collected': True}
2025-07-06 16:02:25,629 - __main__ - INFO - 测试A/B测试性能比较...
2025-07-06 16:02:25,629 - __main__ - INFO - 性能比较结果: {'ab_test_week_1': {'config': {'optimized_agents': []}, 'statistical_comparison': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}}
2025-07-06 16:02:25,629 - __main__ - INFO - 测试选择获胜提示词...
2025-07-06 16:02:25,629 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 16:02:25,629 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 16:02:25,629 - __main__ - INFO - 提示词选择结果: {'ab_test_week_1': {'config': {'optimized_agents': []}, 'agent_selections': {}, 'overall_recommendation': 'optimized'}}
2025-07-06 16:02:25,629 - __main__ - INFO - ✅ 所有测试通过，修复成功！
2025-07-06 16:02:25,630 - __main__ - INFO - 
========================================
2025-07-06 16:02:25,630 - __main__ - INFO - 测试2：边界情况测试
2025-07-06 16:02:25,630 - __main__ - INFO - ========================================
2025-07-06 16:02:25,630 - __main__ - INFO - 测试边界情况...
2025-07-06 16:02:25,630 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:02:25,630 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 16:02:25,630 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 16:02:25,631 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:02:25,631 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 16:02:25,640 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 16:02:25,642 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 16:02:25,642 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 16:02:25,643 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 16:02:25,643 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 16:02:25,643 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 16:02:25,643 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 16:02:25,644 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:02:25,644 - __main__ - INFO - 加载历史数据完成: 0 个实验记录
2025-07-06 16:02:25,644 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 16:02:25,645 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 16:02:25,645 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 16:02:25,645 - __main__ - INFO - 测试空性能比较数据...
2025-07-06 16:02:25,645 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 16:02:25,645 - __main__ - WARNING - 性能比较数据为空
2025-07-06 16:02:25,645 - __main__ - INFO - 空数据结果: {'error': 'empty_performance_comparison'}
2025-07-06 16:02:25,646 - __main__ - INFO - 测试错误类型数据...
2025-07-06 16:02:25,646 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 16:02:25,646 - __main__ - ERROR - 性能比较数据类型错误: <class 'bool'>, 期望dict
2025-07-06 16:02:25,646 - __main__ - INFO - 错误类型数据结果: {'error': 'invalid_input_type', 'input_type': "<class 'bool'>"}
2025-07-06 16:02:25,646 - __main__ - INFO - 测试不完整数据结构...
2025-07-06 16:02:25,646 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 16:02:25,647 - __main__ - ERROR - 实验test_experiment缺少必要字段: config或statistical_comparison
2025-07-06 16:02:25,647 - __main__ - INFO - ✅ 提示词选择完成，处理了0个实验
2025-07-06 16:02:25,647 - __main__ - INFO - 不完整数据结果: {}
2025-07-06 16:02:25,647 - __main__ - INFO - ✅ 边界情况测试通过
2025-07-06 16:02:25,647 - __main__ - INFO - 
============================================================
2025-07-06 16:02:25,647 - __main__ - INFO - 测试总结
2025-07-06 16:02:25,648 - __main__ - INFO - ============================================================
2025-07-06 16:02:25,648 - __main__ - INFO - 通过测试: 2/2
2025-07-06 16:02:25,648 - __main__ - INFO - 🎉 所有测试通过！OPRO系统修复成功！
