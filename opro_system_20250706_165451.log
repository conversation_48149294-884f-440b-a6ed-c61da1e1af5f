2025-07-06 16:54:51,459 - __main__ - INFO - ====================================================================================================
2025-07-06 16:54:51,459 - __main__ - INFO - OPRO系统启动
2025-07-06 16:54:51,459 - __main__ - INFO - ====================================================================================================
2025-07-06 16:54:51,459 - __main__ - INFO - 运行模式: integrated
2025-07-06 16:54:51,459 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 16:54:51,460 - __main__ - INFO - OPRO启用: True
2025-07-06 16:54:51,460 - __main__ - INFO - 数据存储启用: True
2025-07-06 16:54:51,460 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 16:54:51,460 - __main__ - INFO - 初始化系统...
2025-07-06 16:54:51,461 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 16:54:51,461 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 16:54:51,461 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 16:54:51,462 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 16:54:51,462 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 16:54:51,462 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 16:54:51,462 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 16:54:51,462 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 16:54:51,462 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 16:54:51,462 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 16:54:51,463 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 16:54:51,471 - __main__ - INFO - 数据库初始化完成
2025-07-06 16:54:51,472 - __main__ - INFO - 自动备份线程已启动
2025-07-06 16:54:51,472 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 16:54:51,472 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 16:54:51,472 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 16:54:51,493 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 16:54:51,494 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 16:54:51,495 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 16:54:51,512 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 16:54:51,513 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 16:54:51,513 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 16:54:51,515 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 16:54:51,517 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 16:54:51,517 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 16:54:51,517 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 16:54:51,518 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 16:54:51,518 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 16:54:51,539 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:54:51,540 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 16:54:51,540 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 16:54:51,542 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:54:51,544 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 16:54:51,544 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 16:54:51,545 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 16:54:51,545 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 16:54:51,546 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 16:54:51,548 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 16:54:51,551 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 16:54:51,552 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 16:54:51,565 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 16:54:51,567 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 16:54:51,568 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 16:54:51,571 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 16:54:51,576 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 16:54:51,716 - __main__ - ERROR - 创建数据备份失败: [('data/prompts\\TAA\\opt_TAA_20250705_223429_c5c4be04.json', 'data/backups\\backup_20250706_165451\\prompts\\TAA\\opt_TAA_20250705_223429_c5c4be04.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_232158_2ffe76f6.json', 'data/backups\\backup_20250706_165451\\prompts\\TAA\\opt_TAA_20250705_232158_2ffe76f6.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250706_130604_5d2a2aec.json', 'data/backups\\backup_20250706_165451\\prompts\\TAA\\opt_TAA_20250706_130604_5d2a2aec.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_222038_1f75fab2.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_222038_1f75fab2.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_223537_92b0fa86.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_223537_92b0fa86.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_224200_5fc9b021.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_224200_5fc9b021.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_225424_c4e4b48b.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_225424_c4e4b48b.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_230716_3883ac3e.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_230716_3883ac3e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_231739_5e0a6912.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_231739_5e0a6912.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232252_7189effc.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_232252_7189effc.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232550_8b5d2c96.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_232550_8b5d2c96.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250706_130845_ce91b106.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250706_130845_ce91b106.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-06 16:54:51,716 - contribution_assessment.assessor.ContributionAssessor - ERROR - 创建数据备份失败: [('data/prompts\\NOA\\opt_NOA_20250706_130813_88041910.json', 'data/backups\\backup_20250706_165451\\prompts\\NOA\\opt_NOA_20250706_130813_88041910.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_230959_3ff65287.json', 'data/backups\\backup_20250706_165451\\prompts\\TAA\\opt_TAA_20250705_230959_3ff65287.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TAA\\opt_TAA_20250705_232652_4cfdf4d1.json', 'data/backups\\backup_20250706_165451\\prompts\\TAA\\opt_TAA_20250705_232652_4cfdf4d1.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_221451_793056fb.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_221451_793056fb.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_222400_58e79558.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_222400_58e79558.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_223845_e8de2143.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_223845_e8de2143.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_224526_f14eabe7.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_224526_f14eabe7.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_225721_e736628c.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_225721_e736628c.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_231040_899a9795.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_231040_899a9795.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232058_526bab8e.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_232058_526bab8e.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232347_7dc8f556.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_232347_7dc8f556.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250705_232917_6a984190.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250705_232917_6a984190.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/prompts\\TRA\\opt_TRA_20250706_151414_4ade6729.json', 'data/backups\\backup_20250706_165451\\prompts\\TRA\\opt_TRA_20250706_151414_4ade6729.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-06 16:54:51,779 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 16:54:51,780 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 16:54:51,914 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 16:54:51,916 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 16:54:52,056 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 16:54:52,060 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 16:54:52,192 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 16:54:52,192 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 16:54:52,192 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 16:54:52,192 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 16:54:52,193 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 16:54:52,193 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 16:54:52,200 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 16:54:52,201 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 16:54:52,201 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 16:54:52,203 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 16:54:52,203 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 16:54:52,204 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 16:54:52,204 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 16:54:52,204 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 16:54:52,205 - __main__ - INFO - 加载历史数据完成: 0 个实验记录
2025-07-06 16:54:52,206 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 16:54:52,206 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 16:54:52,206 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 16:54:52,206 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 16:54:52,206 - __main__ - INFO - 系统初始化完成
2025-07-06 16:54:52,206 - __main__ - INFO - ================================================================================
2025-07-06 16:54:52,207 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 16:54:52,207 - __main__ - INFO - ================================================================================
2025-07-06 16:54:52,207 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-01-31
2025-07-06 16:54:52,207 - __main__ - INFO - ====================================================================================================
2025-07-06 16:54:52,207 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 16:54:52,207 - __main__ - INFO - ====================================================================================================
2025-07-06 16:54:52,208 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-31
2025-07-06 16:54:52,208 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 16:54:52,208 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 16:54:52,208 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 16:54:52,222 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-31
2025-07-06 16:54:52,222 - __main__ - INFO - 📊 总交易日数: 23
2025-07-06 16:54:52,222 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 16:54:52,222 - __main__ - INFO - 🗓️  最后交易日: 2025-01-31
2025-07-06 16:54:52,222 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 16:54:52,223 - __main__ - INFO - 📊 生成了 3 个7天周期
2025-07-06 16:54:52,223 - __main__ - INFO -    - 基线运行周: 2 个
2025-07-06 16:54:52,223 - __main__ - INFO -    - A/B测试周: 1 个
2025-07-06 16:54:52,223 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 16:54:52,223 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 16:54:52,223 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 16:54:52,224 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 16:54:52,224 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 16:54:52,224 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 16:54:52,224 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 16:54:52,225 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 16:54:52,225 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 16:54:52,225 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 16:54:52,225 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 16:54:52,225 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 16:54:52,226 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 16:54:52,226 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 16:54:52,226 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 16:54:52,226 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 16:54:52,230 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 16:54:52,232 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 16:54:52,232 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 16:54:52,233 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 16:54:52,233 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 16:54:52,233 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 16:54:52,233 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 16:54:52,233 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.034619
2025-07-06 16:54:52,233 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 16:54:52,234 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.011449
2025-07-06 16:54:52,234 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 16:54:52,234 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.061862
2025-07-06 16:54:52,234 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 16:54:52,234 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.021953
2025-07-06 16:54:52,235 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 16:54:52,235 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000820
2025-07-06 16:54:52,235 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 16:54:52,235 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.026937
2025-07-06 16:54:52,235 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 16:54:52,236 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.070828
2025-07-06 16:54:52,236 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.228467 = 大联盟值 0.228467
2025-07-06 16:54:52,236 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-06 16:54:52,237 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 16:54:52,237 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BeOA', 'TAA']
2025-07-06 16:54:52,237 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BeOA', 'TAA']
2025-07-06 16:54:52,238 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BeOA', 'TAA']
2025-07-06 16:54:52,238 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 16:54:52,238 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 16:54:52,238 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 16:54:52,245 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:54:52,246 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:54:52,250 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-06 16:54:52,256 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002BD018F1E20>
2025-07-06 16:54:52,256 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-06 16:54:52,258 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:54:52,258 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-06 16:54:52,259 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:54:52,259 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-06 16:54:52,261 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-06 16:54:52,262 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002BD02087CD0> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-06 16:54:52,469 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002BD021CC6B0>
2025-07-06 16:54:52,469 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:54:52,473 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:54:52,473 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:54:52,474 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:54:52,474 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:54:54,435 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据备份完成: backup_20250706_165452 (3.37 MB)
2025-07-06 16:54:55,400 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:54:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=1a0c65d917517920931366428e00597b3a95e20b7280e2a59a4775b8a0af4c;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165453011be5a596c640ba'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:54:55,402 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:54:55,403 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:54:55,403 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:54:55,404 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:54:55,404 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:54:55,405 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:54:55,408 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:54:55,409 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:54:55,410 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:54:55,410 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:54:55,412 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:54:55,412 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:54:55,412 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:54:58,831 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:54:59 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616545628e359bbb8754cd1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:54:58,832 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:54:58,832 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:54:58,832 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:54:58,833 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:54:58,833 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:54:58,833 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:54:58,835 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:54:58,835 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:54:58,838 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:54:58,839 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:54:58,839 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:54:58,840 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:54:58,840 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:00,771 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:01 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165459d103cc415fea493c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:00,771 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:00,771 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:00,772 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:00,772 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:00,772 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:00,772 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:00,774 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:00,774 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:00,775 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:00,775 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:00,775 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:00,775 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:00,775 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:03,550 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:04 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061655011b50e38dccdc464f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:03,551 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:03,551 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:03,551 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:03,551 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:03,551 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:03,551 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:03,552 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:03,552 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:03,553 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:03,554 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:03,554 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:03,555 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:03,555 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:06,067 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061655042a51d25fa3f44c25'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:06,067 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:06,068 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:06,069 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:06,069 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:06,070 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:06,070 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:06,071 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:06,071 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:06,072 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:06,073 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:06,075 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:06,076 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:06,076 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:09,233 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616550663d324c6c04144ac'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:09,234 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:09,234 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:09,234 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:09,234 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:09,235 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:09,235 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:09,235 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:09,235 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:09,236 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:09,236 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:09,237 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:09,237 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:09,237 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:13,100 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165509676960bd498e41ea'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:13,100 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:13,100 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:13,100 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:13,101 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:13,101 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:13,101 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:13,101 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:13,102 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:13,102 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:13,103 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:13,103 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:13,103 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:13,104 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:17,182 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061655130f517acfa111452a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:17,182 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:17,183 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:17,183 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:17,183 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:17,183 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:17,183 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:17,185 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 16:55:17,201 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_165517_4e53fde0
2025-07-06 16:55:17,202 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_165517_4e53fde0
2025-07-06 16:55:17,204 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_165517_4e53fde0
2025-07-06 16:55:17,204 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.745366
2025-07-06 16:55:17,205 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 16:55:17,205 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 TAA 优化提示词
2025-07-06 16:55:17,206 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 16:55:17,207 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:17,208 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:17,210 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:17,211 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:17,212 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:17,212 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:17,212 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:20,410 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165517a3a6e2b4c3e94272'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:20,410 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:20,410 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:20,410 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:20,410 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:20,411 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:20,411 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:20,411 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:20,411 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:20,412 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:20,412 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:20,412 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:20,413 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:20,413 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:22,725 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165521bc9dab00d183433f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:22,725 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:22,726 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:22,727 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:22,727 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:22,728 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:22,728 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:22,730 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:22,730 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:22,731 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:22,732 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:22,732 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:22,732 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:22,733 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:26,048 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165523130d6385d13e499c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:26,048 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:26,048 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:26,048 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:26,048 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:26,048 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:26,049 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:26,050 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:26,050 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:26,050 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:26,051 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:26,052 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:26,052 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:26,052 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:29,375 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165526e3268969afd3436d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:29,376 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:29,376 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:29,376 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:29,376 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:29,376 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:29,377 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:29,378 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:29,378 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:29,379 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:29,380 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:29,380 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:29,380 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:29,380 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:33,790 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165530907115c6c1544858'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:33,790 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:33,791 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:33,792 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:33,792 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:33,792 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:33,793 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:33,796 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:33,796 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:33,798 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:33,799 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:33,799 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:33,799 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:33,799 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:39,527 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165534ebe27ea072e64e07'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:39,528 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:39,528 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:39,529 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:39,529 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:39,530 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:39,530 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:39,532 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:39,533 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:39,535 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:39,536 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:39,536 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:39,537 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:39,537 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:43,998 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616554027f5a24310944ac2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:43,999 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:43,999 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:44,000 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:44,000 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:44,000 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:44,000 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:44,002 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:44,002 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:44,003 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:44,003 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:44,004 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:44,005 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:44,005 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:47,242 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165544bcca377e1b6e40af'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:47,242 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:47,243 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:47,243 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:47,243 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:47,244 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:47,244 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:47,244 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 16:55:47,252 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_TAA_20250706_165547_dc38a11c
2025-07-06 16:55:47,252 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: TAA -> opt_TAA_20250706_165547_dc38a11c
2025-07-06 16:55:47,252 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_TAA_20250706_165547_dc38a11c
2025-07-06 16:55:47,253 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 TAA 优化完成，最佳候选预期得分: 0.838030
2025-07-06 16:55:47,253 - __main__ - INFO - 代理 TAA 优化完成
2025-07-06 16:55:47,275 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-01
2025-07-06 16:55:47,276 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 16:55:47,276 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 16:55:47,276 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 16:55:47,276 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-09
2025-07-06 16:55:47,277 - __main__ - INFO - 🆕 开始第2周 - A/B测试阶段: 2025-01-09
2025-07-06 16:55:47,277 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第1/7天
2025-07-06 16:55:47,277 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 16:55:47,277 - __main__ - INFO - 🔄 开始第 2 周（ab_testing）: 2025-01-10 到 2025-01-20
2025-07-06 16:55:47,277 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-10
2025-07-06 16:55:47,277 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第2/7天
2025-07-06 16:55:47,277 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-13
2025-07-06 16:55:47,278 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第5/7天
2025-07-06 16:55:47,278 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-14
2025-07-06 16:55:47,278 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第6/7天
2025-07-06 16:55:47,278 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-15
2025-07-06 16:55:47,278 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第7/7天
2025-07-06 16:55:47,278 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 16:55:47,278 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 16:55:47,278 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 16:55:47,278 - __main__ - INFO - 🔄 开始更新智能体配置...
2025-07-06 16:55:47,278 - __main__ - INFO - ✅ 智能体 BeOA 配置更新成功: optimized
2025-07-06 16:55:47,279 - __main__ - INFO - ✅ 智能体 TAA 配置更新成功: optimized
2025-07-06 16:55:47,279 - __main__ - INFO - 🔄 智能体配置更新完成: 成功2, 失败0
2025-07-06 16:55:47,279 - __main__ - INFO - 已注册获胜实验结果到迭代Shapley计算器
2025-07-06 16:55:47,279 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-16
2025-07-06 16:55:47,279 - __main__ - INFO - 🆕 开始第3周 - 基线运行阶段: 2025-01-16
2025-07-06 16:55:47,279 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第1/7天
2025-07-06 16:55:47,279 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-17
2025-07-06 16:55:47,279 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第2/7天
2025-07-06 16:55:47,279 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-20
2025-07-06 16:55:47,279 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第5/7天
2025-07-06 16:55:47,280 - __main__ - INFO - ✅ 第 2 周完成: ab_testing_complete
2025-07-06 16:55:47,280 - __main__ - INFO - 🔬 第 2 周（A/B测试）完成后运行迭代Shapley计算...
2025-07-06 16:55:47,280 - __main__ - WARNING - ⚠️  第 2 周没有获胜实验数据，跳过迭代Shapley计算
2025-07-06 16:55:47,280 - __main__ - INFO - 🔄 开始第 3 周（baseline_operation）: 2025-01-21 到 2025-01-29
2025-07-06 16:55:47,280 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-21
2025-07-06 16:55:47,280 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第6/7天
2025-07-06 16:55:47,280 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-22
2025-07-06 16:55:47,280 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第7/7天
2025-07-06 16:55:47,280 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 16:55:47,281 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 16:55:47,281 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 16:55:47,284 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 16:55:47,284 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 16:55:47,285 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 16:55:47,285 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 16:55:47,285 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 16:55:47,285 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 16:55:47,285 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 16:55:47,285 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.034619
2025-07-06 16:55:47,285 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 16:55:47,286 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.011449
2025-07-06 16:55:47,286 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 16:55:47,286 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.061862
2025-07-06 16:55:47,286 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 16:55:47,286 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.021953
2025-07-06 16:55:47,286 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 16:55:47,286 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000820
2025-07-06 16:55:47,286 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 16:55:47,287 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.026937
2025-07-06 16:55:47,287 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 16:55:47,287 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.070828
2025-07-06 16:55:47,287 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.228467 = 大联盟值 0.228467
2025-07-06 16:55:47,287 - __main__ - INFO - Shapley值计算完成，耗时 0.003s
2025-07-06 16:55:47,288 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 16:55:47,288 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BeOA', 'TAA']
2025-07-06 16:55:47,288 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BeOA', 'TAA']
2025-07-06 16:55:47,289 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BeOA', 'TAA']
2025-07-06 16:55:47,289 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-16
2025-07-06 16:55:47,289 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 16:55:47,289 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 16:55:47,290 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:47,290 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:47,291 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:47,292 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:47,292 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:47,292 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:47,292 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:50,905 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:51 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616554737bb25bfbe454f55'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:50,905 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:50,906 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:50,907 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:50,907 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:50,907 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:50,908 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:50,909 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:50,910 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:50,911 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:50,912 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:50,912 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:50,913 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:50,913 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:54,285 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061655511dc7dc3e089d40bc'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:54,287 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:54,287 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:54,288 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:54,288 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:54,288 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:54,289 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:54,290 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:54,291 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:54,292 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:54,292 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:54,293 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:54,293 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:54,294 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:55:58,791 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:55:59 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061655546cd176bc2dbd449a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:55:58,792 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:55:58,792 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:55:58,793 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:55:58,794 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:55:58,794 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:55:58,795 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:55:58,796 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:55:58,797 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:55:58,798 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:55:58,799 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:55:58,800 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:55:58,800 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:55:58,801 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:01,607 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616555948cfd95abc9b44a8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:01,608 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:01,608 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:01,608 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:01,609 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:01,609 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:01,609 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:01,610 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:01,610 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:01,611 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:01,611 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:01,611 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:01,611 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:01,611 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:05,195 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:05 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616560240ee95468a274bb5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:05,196 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:05,196 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:05,197 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:05,197 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:05,198 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:05,198 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:05,201 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:05,201 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:05,203 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:05,204 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:05,205 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:05,205 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:05,205 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:09,100 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061656050700cbf30be64ab6'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:09,101 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:09,101 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:09,102 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:09,102 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:09,102 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:09,102 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:09,103 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:09,104 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:09,105 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:09,106 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:09,106 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:09,107 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:09,107 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:12,278 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165609cf69e70efc2949e5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:12,279 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:12,279 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:12,280 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:12,281 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:12,281 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:12,282 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:12,283 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:12,283 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:12,284 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:12,285 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:12,286 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:12,286 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:12,286 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:16,017 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165612408f856687ab4e18'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:16,017 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:16,018 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:16,018 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:16,018 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:16,018 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:16,018 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:16,019 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 16:56:16,027 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_165616_c29bc725
2025-07-06 16:56:16,028 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_165616_c29bc725
2025-07-06 16:56:16,028 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_165616_c29bc725
2025-07-06 16:56:16,028 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.762971
2025-07-06 16:56:16,028 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 16:56:16,028 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 TAA 优化提示词
2025-07-06 16:56:16,028 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 16:56:16,029 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:16,029 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:16,030 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:16,030 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:16,031 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:16,031 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:16,031 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:19,221 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061656160e9c1f31e9c84049'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:19,222 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:19,222 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:19,222 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:19,222 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:19,223 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:19,223 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:19,225 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:19,225 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:19,227 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:19,228 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:19,228 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:19,229 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:19,229 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:22,401 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061656193d32c305e01742b5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:22,402 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:22,402 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:22,403 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:22,404 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:22,405 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:22,405 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:22,406 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:22,407 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:22,408 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:22,408 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:22,409 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:22,409 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:22,409 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:25,611 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165623f8f487061ba34673'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:25,611 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:25,612 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:25,613 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:25,613 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:25,614 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:25,614 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:25,616 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:25,617 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:25,618 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:25,619 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:25,619 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:25,620 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:25,620 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:27,940 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:28 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061656262d6ed4ebbb7f4161'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:27,941 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:27,942 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:27,942 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:27,942 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:27,942 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:27,942 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:27,943 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:27,943 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:27,944 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:27,945 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:27,945 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:27,945 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:27,945 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:30,778 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616562813b1a25fdd824ed9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:30,779 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:30,779 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:30,780 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:30,780 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:30,780 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:30,780 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:30,783 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:30,783 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:30,784 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:30,784 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:30,785 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:30,785 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:30,785 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:32,888 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:33 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070616563119efb8e2460f42a3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:32,889 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:32,890 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:32,890 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:32,890 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:32,891 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:32,891 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:32,892 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:32,892 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:32,894 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:32,894 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:32,895 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:32,895 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:32,896 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:36,157 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165633cebb27c9eafd4fda'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:36,157 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:36,158 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:36,158 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:36,159 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:36,159 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:36,159 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:36,161 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 16:56:36,162 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 16:56:36,162 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 16:56:36,162 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 16:56:36,163 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 16:56:36,163 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 16:56:36,163 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 16:56:41,829 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 08:56:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706165636bdc64bb1334a424c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 16:56:41,830 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 16:56:41,830 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 16:56:41,831 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 16:56:41,831 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 16:56:41,832 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 16:56:41,832 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 16:56:41,834 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 16:56:41,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_TAA_20250706_165641_97b460aa
2025-07-06 16:56:41,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: TAA -> opt_TAA_20250706_165641_97b460aa
2025-07-06 16:56:41,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_TAA_20250706_165641_97b460aa
2025-07-06 16:56:41,842 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 TAA 优化完成，最佳候选预期得分: 0.786009
2025-07-06 16:56:41,842 - __main__ - INFO - 代理 TAA 优化完成
2025-07-06 16:56:41,860 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-16
2025-07-06 16:56:41,860 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 16:56:41,860 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 16:56:41,860 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-23
2025-07-06 16:56:41,860 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-24
2025-07-06 16:56:41,861 - __main__ - INFO - 🆕 开始第4周 - A/B测试阶段: 2025-01-24
2025-07-06 16:56:41,861 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第1/7天
2025-07-06 16:56:41,861 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-27
2025-07-06 16:56:41,861 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第4/7天
2025-07-06 16:56:41,861 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-28
2025-07-06 16:56:41,861 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第5/7天
2025-07-06 16:56:41,861 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-29
2025-07-06 16:56:41,861 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第6/7天
2025-07-06 16:56:41,861 - __main__ - INFO - ✅ 第 3 周完成: baseline_complete
2025-07-06 16:56:41,861 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 16:56:41,862 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 16:56:41,862 - __main__ - INFO -   - 总周期数: 3
2025-07-06 16:56:41,862 - __main__ - INFO -   - 成功周期: 3
2025-07-06 16:56:41,862 - __main__ - INFO -   - 失败周期: 0
2025-07-06 16:56:41,862 - __main__ - INFO -   - 成功率: 100.00%
2025-07-06 16:56:41,862 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 16:56:41,862 - __main__ - INFO - ====================================================================================================
2025-07-06 16:56:41,862 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 16:56:41,862 - __main__ - INFO - 📊 总周期数: 3
2025-07-06 16:56:41,862 - __main__ - INFO - 📊 总交易天数: 23
2025-07-06 16:56:41,862 - __main__ - INFO - ⏱️  总执行时间: 109.65秒
2025-07-06 16:56:41,863 - __main__ - INFO - ====================================================================================================
2025-07-06 16:56:41,863 - __main__ - INFO - 开始交易会话: assessment_20250706_165641
2025-07-06 16:56:41,863 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 16:56:41,866 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_165641
2025-07-06 16:56:41,866 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_165641 (系统盈亏: 0.00)
2025-07-06 16:56:41,867 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_165641
2025-07-06 16:56:41,867 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 16:56:41,867 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 16:56:41,867 - __main__ - INFO - ====================================================================================================
2025-07-06 16:56:41,867 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 16:56:41,867 - __main__ - INFO - ====================================================================================================
