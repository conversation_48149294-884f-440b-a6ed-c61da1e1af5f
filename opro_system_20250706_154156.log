2025-07-06 15:41:56,573 - __main__ - INFO - ====================================================================================================
2025-07-06 15:41:56,574 - __main__ - INFO - OPRO系统启动
2025-07-06 15:41:56,574 - __main__ - INFO - ====================================================================================================
2025-07-06 15:41:56,574 - __main__ - INFO - 运行模式: integrated
2025-07-06 15:41:56,574 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 15:41:56,574 - __main__ - INFO - OPRO启用: True
2025-07-06 15:41:56,574 - __main__ - INFO - 数据存储启用: True
2025-07-06 15:41:56,574 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 15:41:56,576 - __main__ - INFO - 初始化系统...
2025-07-06 15:41:56,576 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 15:41:56,576 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 15:41:56,576 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 15:41:56,578 - __main__ - INFO - 数据库初始化完成
2025-07-06 15:41:56,579 - __main__ - INFO - 自动备份线程已启动
2025-07-06 15:41:56,579 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 15:41:56,580 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 15:41:56,580 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 15:41:56,584 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 15:41:56,584 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 15:41:56,584 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 15:41:56,597 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 15:41:56,598 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 15:41:56,598 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 15:41:56,600 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 15:41:56,600 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 15:41:56,600 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 15:41:56,600 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 15:41:56,600 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 15:41:56,623 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 15:41:56,623 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 15:41:56,624 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 15:41:56,624 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 15:41:56,625 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 15:41:56,625 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 15:41:56,626 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 15:41:56,626 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 15:41:56,626 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 15:41:56,629 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 15:41:56,630 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 15:41:56,630 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 15:41:56,636 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 15:41:56,636 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 15:41:56,637 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 15:41:57,018 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 15:41:57,019 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 15:41:57,118 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 15:41:57,119 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 15:41:57,120 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 15:41:57,120 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 15:41:57,120 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 15:41:57,120 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 15:41:57,126 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 15:41:57,127 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 15:41:57,128 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 15:41:57,129 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 15:41:57,129 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 15:41:57,130 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 15:41:57,130 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 15:41:57,131 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 15:41:57,132 - __main__ - INFO - 加载历史数据完成: 0 个实验记录
2025-07-06 15:41:57,132 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 15:41:57,132 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 15:41:57,132 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 15:41:57,132 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 15:41:57,134 - __main__ - INFO - 系统初始化完成
2025-07-06 15:41:57,134 - __main__ - INFO - ================================================================================
2025-07-06 15:41:57,134 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 15:41:57,134 - __main__ - INFO - ================================================================================
2025-07-06 15:41:57,134 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-01-31
2025-07-06 15:41:57,134 - __main__ - INFO - ====================================================================================================
2025-07-06 15:41:57,135 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 15:41:57,135 - __main__ - INFO - ====================================================================================================
2025-07-06 15:41:57,135 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-31
2025-07-06 15:41:57,135 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 15:41:57,135 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 15:41:57,135 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 15:41:57,136 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-31
2025-07-06 15:41:57,137 - __main__ - INFO - 📊 总交易日数: 23
2025-07-06 15:41:57,137 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 15:41:57,137 - __main__ - INFO - 🗓️  最后交易日: 2025-01-31
2025-07-06 15:41:57,137 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 15:41:57,138 - __main__ - INFO - 📊 生成了 3 个7天周期
2025-07-06 15:41:57,138 - __main__ - INFO -    - 基线运行周: 2 个
2025-07-06 15:41:57,138 - __main__ - INFO -    - A/B测试周: 1 个
2025-07-06 15:41:57,138 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 15:41:57,138 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 15:41:57,139 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 15:41:57,139 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 15:41:57,139 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 15:41:57,139 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 15:41:57,140 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 15:41:57,140 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 15:41:57,140 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 15:41:57,140 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 15:41:57,140 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 15:41:57,140 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 15:41:57,141 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 15:41:57,141 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 15:41:57,141 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 15:41:57,141 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 15:41:57,145 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 15:41:57,145 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 15:41:57,146 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 15:41:57,147 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 15:41:57,147 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 15:41:57,148 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 15:41:57,148 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 15:41:57,148 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 15:41:57,149 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BeOA', 'BOA']
2025-07-06 15:41:57,149 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 15:41:57,149 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 15:41:57,149 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 15:41:58,439 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_154156\\trading\\2025-01-02\\BeOA\\outputs.json'
2025-07-06 15:41:58,448 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据备份完成: backup_20250706_154156 (3.32 MB)
2025-07-06 15:41:58,934 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据备份完成: backup_20250706_154157 (3.32 MB)
2025-07-06 15:42:01,990 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:05,563 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:10,115 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:12,684 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:16,825 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:19,597 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:23,854 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:26,864 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:26,867 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 15:42:26,880 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_154226_6027c255
2025-07-06 15:42:26,880 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_154226_6027c255
2025-07-06 15:42:26,881 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_154226_6027c255
2025-07-06 15:42:26,881 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.954104
2025-07-06 15:42:26,881 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 15:42:26,881 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 15:42:26,881 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 15:42:29,796 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:34,023 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:38,290 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:40,968 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:43,594 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:46,539 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:50,746 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:53,942 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:42:53,944 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 15:42:53,956 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_154253_ee465f98
2025-07-06 15:42:53,956 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_154253_ee465f98
2025-07-06 15:42:53,956 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_154253_ee465f98
2025-07-06 15:42:53,957 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.753451
2025-07-06 15:42:53,957 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 15:42:53,990 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-01
2025-07-06 15:42:53,991 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 15:42:53,991 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 15:42:53,991 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-08
2025-07-06 15:42:53,992 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-09
2025-07-06 15:42:53,993 - __main__ - INFO - 🆕 开始第2周 - A/B测试阶段: 2025-01-09
2025-07-06 15:42:53,993 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第1/7天
2025-07-06 15:42:53,993 - __main__ - INFO - ✅ 第 1 周完成: baseline_complete
2025-07-06 15:42:53,993 - __main__ - INFO - 🔄 开始第 2 周（ab_testing）: 2025-01-10 到 2025-01-20
2025-07-06 15:42:53,993 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-10
2025-07-06 15:42:53,994 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第2/7天
2025-07-06 15:42:53,994 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-13
2025-07-06 15:42:53,994 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第5/7天
2025-07-06 15:42:53,995 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-14
2025-07-06 15:42:53,995 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第6/7天
2025-07-06 15:42:53,995 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-15
2025-07-06 15:42:53,995 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第2周，第7/7天
2025-07-06 15:42:53,996 - __main__ - INFO - 📈 A/B测试周完成，开始性能比较和决策...
2025-07-06 15:42:53,996 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 15:42:53,996 - __main__ - ERROR - 7天连续优化循环执行失败: 'bool' object is not subscriptable
2025-07-06 15:42:53,996 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-16
2025-07-06 15:42:53,997 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-17
2025-07-06 15:42:53,997 - __main__ - INFO - 🆕 开始第3周 - 基线运行阶段: 2025-01-17
2025-07-06 15:42:53,997 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第1/7天
2025-07-06 15:42:53,998 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-20
2025-07-06 15:42:53,998 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第4/7天
2025-07-06 15:42:53,998 - __main__ - INFO - ✅ 第 2 周完成: in_progress
2025-07-06 15:42:53,999 - __main__ - INFO - 🔄 开始第 3 周（baseline_operation）: 2025-01-21 到 2025-01-29
2025-07-06 15:42:53,999 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-21
2025-07-06 15:42:54,000 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第5/7天
2025-07-06 15:42:54,000 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-22
2025-07-06 15:42:54,000 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第6/7天
2025-07-06 15:42:54,000 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-23
2025-07-06 15:42:54,001 - __main__ - INFO - 📊 执行基线运行阶段 - 第3周，第7/7天
2025-07-06 15:42:54,001 - __main__ - INFO - 🎯 基线运行周完成，开始Shapley分析和提示词优化...
2025-07-06 15:42:54,001 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 15:42:54,002 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 15:42:54,006 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 15:42:54,007 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 15:42:54,007 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 15:42:54,008 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 15:42:54,009 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-06 15:42:54,009 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 15:42:54,010 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 15:42:54,010 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 15:42:54,010 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BeOA', 'BOA']
2025-07-06 15:42:54,010 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-17
2025-07-06 15:42:54,011 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 15:42:54,011 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 15:42:57,608 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:02,858 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:08,060 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:10,839 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:13,351 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:16,870 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:21,566 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:25,677 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:25,679 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 15:43:25,691 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_154325_6f0c7b06
2025-07-06 15:43:25,691 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_154325_6f0c7b06
2025-07-06 15:43:25,691 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_154325_6f0c7b06
2025-07-06 15:43:25,691 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.781870
2025-07-06 15:43:25,692 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 15:43:25,692 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 15:43:25,692 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 15:43:29,179 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:33,667 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:36,518 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:41,067 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:45,562 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:51,551 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:54,290 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:57,223 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 15:43:57,225 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 15:43:57,232 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BOA_20250706_154357_b078fc1a
2025-07-06 15:43:57,232 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_154357_b078fc1a
2025-07-06 15:43:57,232 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BOA_20250706_154357_b078fc1a
2025-07-06 15:43:57,234 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.791963
2025-07-06 15:43:57,235 - __main__ - INFO - 代理 BOA 优化完成
2025-07-06 15:43:57,254 - __main__ - INFO - 每周优化循环完成 - 周期: 2025-01-17
2025-07-06 15:43:57,255 - __main__ - INFO - ✅ OPRP优化完成，成功优化2个智能体
2025-07-06 15:43:57,255 - __main__ - INFO - 已存储2个智能体的优化提示词
2025-07-06 15:43:57,255 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-24
2025-07-06 15:43:57,255 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-27
2025-07-06 15:43:57,255 - __main__ - INFO - 🆕 开始第4周 - A/B测试阶段: 2025-01-27
2025-07-06 15:43:57,255 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第1/7天
2025-07-06 15:43:57,256 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-28
2025-07-06 15:43:57,256 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第2/7天
2025-07-06 15:43:57,256 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-29
2025-07-06 15:43:57,256 - __main__ - INFO - 🧪 执行A/B测试阶段 - 第4周，第3/7天
2025-07-06 15:43:57,256 - __main__ - INFO - ✅ 第 3 周完成: baseline_complete
2025-07-06 15:43:57,256 - __main__ - INFO - 步骤4: 计算整体交易统计...
2025-07-06 15:43:57,256 - __main__ - INFO - 📊 增强交易统计计算完成:
2025-07-06 15:43:57,256 - __main__ - INFO -   - 总周期数: 3
2025-07-06 15:43:57,256 - __main__ - INFO -   - 成功周期: 2
2025-07-06 15:43:57,257 - __main__ - INFO -   - 失败周期: 1
2025-07-06 15:43:57,257 - __main__ - INFO -   - 成功率: 66.67%
2025-07-06 15:43:57,257 - __main__ - INFO -   - Shapley计算: 0/0
2025-07-06 15:43:57,257 - __main__ - INFO - ====================================================================================================
2025-07-06 15:43:57,257 - __main__ - INFO - 🎉 增强的完整日期范围交易系统执行完成!
2025-07-06 15:43:57,257 - __main__ - INFO - 📊 总周期数: 3
2025-07-06 15:43:57,257 - __main__ - INFO - 📊 总交易天数: 23
2025-07-06 15:43:57,257 - __main__ - INFO - ⏱️  总执行时间: 120.12秒
2025-07-06 15:43:57,257 - __main__ - INFO - ====================================================================================================
2025-07-06 15:43:57,258 - __main__ - INFO - 开始交易会话: assessment_20250706_154357
2025-07-06 15:43:57,263 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_154357
2025-07-06 15:43:57,264 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_154357 (系统盈亏: 0.00)
2025-07-06 15:43:57,264 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_154357
2025-07-06 15:43:57,264 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 15:43:57,264 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 15:43:57,264 - __main__ - INFO - ====================================================================================================
2025-07-06 15:43:57,265 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 15:43:57,265 - __main__ - INFO - ====================================================================================================
