2025-07-06 17:06:53,251 - __main__ - INFO - ======================================================================
2025-07-06 17:06:53,251 - __main__ - INFO - 开始OPRO系统获胜数据集成测试
2025-07-06 17:06:53,251 - __main__ - INFO - ======================================================================
2025-07-06 17:06:53,251 - __main__ - INFO - 
==================================================
2025-07-06 17:06:53,251 - __main__ - INFO - 测试1：模拟A/B测试周完成流程
2025-07-06 17:06:53,251 - __main__ - INFO - ==================================================
2025-07-06 17:06:53,251 - __main__ - INFO - 模拟A/B测试周完成的完整流程
2025-07-06 17:06:53,253 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,253 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:06:53,253 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:06:53,254 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,254 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:06:53,260 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:06:53,261 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:06:53,261 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:06:53,263 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:06:53,263 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:06:53,263 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:06:53,264 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:06:53,264 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,265 - __main__ - INFO - 加载历史数据完成: 1 个实验记录
2025-07-06 17:06:53,265 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:06:53,266 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:06:53,266 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:06:53,267 - __main__ - INFO - ✅ 管理器创建成功
2025-07-06 17:06:53,267 - __main__ - INFO - 🔄 模拟A/B测试数据收集...
2025-07-06 17:06:53,267 - __main__ - INFO - A/B测试数据: {'week_number': 2, 'test_agents': ['BeOA', 'TAA'], 'original_performance': {}, 'optimized_performance': {}, 'data_collected': True}
2025-07-06 17:06:53,267 - __main__ - INFO - 📊 模拟性能比较...
2025-07-06 17:06:53,267 - __main__ - INFO - 性能比较结果: {'ab_test_week_2': {'config': {'optimized_agents': ['BeOA', 'TAA']}, 'statistical_comparison': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}}
2025-07-06 17:06:53,267 - __main__ - INFO - 🏆 模拟提示词选择...
2025-07-06 17:06:53,267 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:06:53,267 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:06:53,268 - __main__ - INFO - 提示词选择结果: {'ab_test_week_2': {'config': {'optimized_agents': ['BeOA', 'TAA']}, 'agent_selections': {'BeOA': {'selected_prompt': 'optimized', 'reason': '优化提示词表现更好 (改进: 0.0500, p值: 0.0100)', 'statistical_data': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}, 'TAA': {'selected_prompt': 'optimized', 'reason': '优化提示词表现更好 (改进: 0.0500, p值: 0.0100)', 'statistical_data': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}}, 'overall_recommendation': 'optimized'}}
2025-07-06 17:06:53,268 - __main__ - INFO - 📝 注册获胜结果到迭代Shapley计算器...
2025-07-06 17:06:53,268 - __main__ - INFO - 📝 注册实验结果到迭代Shapley计算器...
2025-07-06 17:06:53,268 - __main__ - INFO - 📝 注册实验结果: enhanced_opro_20250706_170653
2025-07-06 17:06:53,268 - __main__ - INFO - ✅ 实验结果注册完成: enhanced_opro_20250706_170653 - 0 条记录
2025-07-06 17:06:53,269 - __main__ - INFO - ✅ 实验结果注册成功: enhanced_opro_20250706_170653
2025-07-06 17:06:53,269 - __main__ - INFO - ✅ 已成功注册获胜实验结果到迭代Shapley计算器
2025-07-06 17:06:53,269 - __main__ - INFO - 📋 生成周期总结...
2025-07-06 17:06:53,269 - __main__ - INFO - 🔍 验证周期总结...
2025-07-06 17:06:53,269 - __main__ - INFO - 获胜实验数据: {'ab_test_data': {'week_number': 2, 'test_agents': ['BeOA', 'TAA'], 'original_performance': {}, 'optimized_performance': {}, 'data_collected': True}, 'performance_comparison': {'ab_test_week_2': {'config': {'optimized_agents': ['BeOA', 'TAA']}, 'statistical_comparison': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}}, 'prompt_selection_result': {'ab_test_week_2': {'config': {'optimized_agents': ['BeOA', 'TAA']}, 'agent_selections': {'BeOA': {'selected_prompt': 'optimized', 'reason': '优化提示词表现更好 (改进: 0.0500, p值: 0.0100)', 'statistical_data': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}, 'TAA': {'selected_prompt': 'optimized', 'reason': '优化提示词表现更好 (改进: 0.0500, p值: 0.0100)', 'statistical_data': {'recommendation': 'optimized', 'improvement': 0.05, 'p_value': 0.01, 'statistical_significance': True, 'comparison_complete': True, 'winning_track': 'optimized'}}}, 'overall_recommendation': 'optimized'}}, 'target_agents': ['BeOA', 'TAA'], 'week_number': 2}
2025-07-06 17:06:53,270 - __main__ - INFO - 🔬 模拟主系统中的迭代Shapley计算触发...
2025-07-06 17:06:53,270 - __main__ - INFO - ✅ 发现获胜实验数据，运行迭代Shapley计算...
2025-07-06 17:06:53,270 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 17:06:53,270 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 17:06:53,275 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 17:06:53,275 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 17:06:53,275 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 17:06:53,275 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 17:06:53,276 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-06 17:06:53,276 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 17:06:53,276 - __main__ - INFO - ✅ 迭代Shapley计算成功完成
2025-07-06 17:06:53,276 - __main__ - INFO - Shapley值: {'NAA': 0.04745893787076074, 'TAA': 0.02295859688107246, 'FAA': 0.041301701780415374, 'BOA': 0.010898451014166453, 'BeOA': -0.0012857482563241305, 'NOA': 0.03734265954043224, 'TRA': 0.06764520988128878}
2025-07-06 17:06:53,277 - __main__ - INFO - 
==================================================
2025-07-06 17:06:53,277 - __main__ - INFO - 测试2：周期总结结构完整性
2025-07-06 17:06:53,277 - __main__ - INFO - ==================================================
2025-07-06 17:06:53,277 - __main__ - INFO - 测试周期总结结构的完整性
2025-07-06 17:06:53,277 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,277 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:06:53,278 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:06:53,278 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,278 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:06:53,286 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:06:53,288 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:06:53,289 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:06:53,290 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:06:53,290 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:06:53,291 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:06:53,291 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:06:53,291 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,315 - __main__ - INFO - 加载历史数据完成: 2 个实验记录
2025-07-06 17:06:53,315 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:06:53,316 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:06:53,316 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:06:53,316 - __main__ - INFO - ✅ 周期总结结构完整
2025-07-06 17:06:53,316 - __main__ - INFO - ✅ 获胜实验数据结构完整
2025-07-06 17:06:53,317 - __main__ - INFO - 
==================================================
2025-07-06 17:06:53,317 - __main__ - INFO - 测试3：数据流一致性
2025-07-06 17:06:53,317 - __main__ - INFO - ==================================================
2025-07-06 17:06:53,317 - __main__ - INFO - 测试数据流的一致性
2025-07-06 17:06:53,317 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,318 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 17:06:53,318 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 17:06:53,318 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,318 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 17:06:53,334 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 17:06:53,335 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 17:06:53,335 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 17:06:53,337 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 17:06:53,337 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 17:06:53,338 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 17:06:53,339 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 17:06:53,339 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 17:06:53,342 - __main__ - INFO - 加载历史数据完成: 2 个实验记录
2025-07-06 17:06:53,342 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 17:06:53,342 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 17:06:53,343 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 17:06:53,343 - __main__ - INFO - 🏆 开始选择获胜提示词...
2025-07-06 17:06:53,343 - __main__ - INFO - ✅ 提示词选择完成，处理了1个实验
2025-07-06 17:06:53,344 - __main__ - INFO - ✅ 周数数据一致
2025-07-06 17:06:53,344 - __main__ - INFO - ✅ 智能体数据一致
2025-07-06 17:06:53,344 - __main__ - INFO - ✅ 数据流完整性验证通过
2025-07-06 17:06:53,345 - __main__ - INFO - 
======================================================================
2025-07-06 17:06:53,345 - __main__ - INFO - 测试总结
2025-07-06 17:06:53,346 - __main__ - INFO - ======================================================================
2025-07-06 17:06:53,346 - __main__ - INFO - 通过测试: 3/3
2025-07-06 17:06:53,347 - __main__ - INFO - 🎉 所有测试通过！OPRO系统获胜数据集成修复成功！
2025-07-06 17:06:53,347 - __main__ - INFO - 现在第2周的获胜实验数据应该能够正确传递给迭代Shapley计算
